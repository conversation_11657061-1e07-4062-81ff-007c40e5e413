import psycopg2

PROFILE_DB_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

def update_email_and_link():
    conn = psycopg2.connect(PROFILE_DB_URL)
    cursor = conn.cursor()

    public_identifier = 'leonardo-aguilar-23a656184'
    email = '<EMAIL>'
    linkedin_url = 'https://www.linkedin.com/in/ACoAACRm0XsBvSibtYSiZ8eVztKTehJle0Fs9Mw'

    print("🧪 Updating email and linkedin_url for existing profile...")

    cursor.execute("""
        UPDATE linkedin_profiles
        SET email = %s,
            linkedin_url = %s
        WHERE public_identifier = %s
    """, (email, linkedin_url, public_identifier))
    conn.commit()

    print(f"✅ Rows affected: {cursor.rowcount}")

    cursor.execute("""
        SELECT full_name, email, linkedin_url, headline, public_identifier
        FROM linkedin_profiles
        WHERE public_identifier = %s
    """, (public_identifier,))
    row = cursor.fetchone()
    print("\n🔍 Updated profile:")
    print(row)

    cursor.close()
    conn.close()
    print("✅ Done!")

if __name__ == "__main__":
    update_email_and_link()