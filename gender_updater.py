import psycopg2
import json
import tempfile
import requests
from deepface import DeepFace

# ---- CONFIG ----
DB_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

# ---- FUNCTIONS ----
def ensure_gender_column():
    conn = psycopg2.connect(DB_URL)
    cursor = conn.cursor()
    cursor.execute("""
        DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1
                FROM information_schema.columns
                WHERE table_name='linkedin_profiles' AND column_name='gender'
            ) THEN
                ALTER TABLE linkedin_profiles ADD COLUMN gender TEXT;
                RAISE NOTICE '✅ Added gender column to linkedin_profiles table.';
            END IF;
        END
        $$;
    """)
    conn.commit()
    cursor.close()
    conn.close()


def get_largest_avatar_url(avatars):
    if not avatars:
        return None
    if isinstance(avatars, str):
        avatars = json.loads(avatars)
    sorted_avatars = sorted(avatars, key=lambda a: a.get("width", 0), reverse=True)
    return sorted_avatars[0].get("url") if sorted_avatars else None


def get_gender_from_image_url(image_url):
    try:
        response = requests.get(image_url, stream=True, timeout=10)
        if response.status_code != 200:
            print(f"⚠️ Failed to download image: {image_url}")
            return None

        with tempfile.NamedTemporaryFile(suffix=".jpg", delete=True) as tmp:
            tmp.write(response.content)
            tmp.flush()
            result = DeepFace.analyze(img_path=tmp.name, actions=["gender"], enforce_detection=False)
            return result[0]["gender"]
    except Exception as e:
        print(f"❌ Error analyzing gender from {image_url}: {e}")
        return None


def update_gender():
    ensure_gender_column()

    conn = psycopg2.connect(DB_URL)
    cursor = conn.cursor()

    cursor.execute("""
        SELECT id, avatar FROM linkedin_profiles
        WHERE gender IS NULL AND avatar IS NOT NULL
    """)
    rows = cursor.fetchall()
    print(f"🧠 Found {len(rows)} profiles without gender.")

    for profile_id, avatar_json in rows:
        avatar_url = get_largest_avatar_url(avatar_json)
        if not avatar_url:
            continue

        gender = get_gender_from_image_url(avatar_url)
        if not gender:
            continue

        cursor.execute("""
            UPDATE linkedin_profiles
            SET gender = %s
            WHERE id = %s
        """, (gender, profile_id))
        print(f"✅ Updated gender for ID: {profile_id} → {gender}")
        conn.commit()

    cursor.close()
    conn.close()
    print("🎉 Done updating genders!")


if __name__ == "__main__":
    update_gender()
