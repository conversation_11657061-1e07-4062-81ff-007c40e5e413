import json
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime

# --- CONFIG ---
JSON_FILE_PATH = "updated_profiles_20250605_115935.json"  # ← replace with your actual file path
PROFILE_DB_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

# --- Connect DB ---
conn = psycopg2.connect(PROFILE_DB_URL)
cur = conn.cursor()

# --- Ensure table exists ---
cur.execute("""
CREATE TABLE IF NOT EXISTS linkedin_companies (
    company_id TEXT PRIMARY KEY,
    universal_name TEXT,
    name TEXT,
    company_size TEXT,
    industry TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
""")
conn.commit()

# --- Load JSON ---
with open(JSON_FILE_PATH, "r", encoding="utf-8") as f:
    profiles = json.load(f)

# --- Build map from company_name → company_id using experiences in DB ---
print("🔍 Building company name → ID map from DB...")
cur = conn.cursor(cursor_factory=RealDictCursor)
cur.execute("SELECT experiences FROM linkedin_profiles")
rows = cur.fetchall()

company_name_to_id = {}

for row in rows:
    experiences_raw = row.get("experiences")
    try:
        experiences = json.loads(experiences_raw) if isinstance(experiences_raw, str) else experiences_raw
        for exp in experiences:
            if isinstance(exp, dict):
                company = exp.get("company", {})
                company_id = company.get("id")
                company_name = company.get("name", "").strip().lower()
                if company_id and company_name:
                    company_name_to_id[company_name] = company_id
    except Exception as e:
        print(f"⚠️ Failed to parse experiences: {e}")

print(f"✅ Loaded {len(company_name_to_id)} unique company names from DB.")

# --- Extract from enriched JSON ---
unique_companies = {}

for profile in profiles:
    name = profile.get("current_company")
    size = profile.get("current_company_size")
    industry = profile.get("industry")

    if not name:
        continue

    lookup_name = name.strip().lower()
    company_id = company_name_to_id.get(lookup_name)

    if not company_id:
        print(f"❌ Skipping: '{name}' not found in DB")
        continue

    if company_id in unique_companies:
        continue

    unique_companies[company_id] = {
        "company_id": company_id,
        "name": name,
        "company_size": str(size) if size is not None else None,
        "industry": industry,
    }

# --- Insert into DB ---
for company in unique_companies.values():
    cur.execute("""
        INSERT INTO linkedin_companies (company_id, name, company_size, industry, updated_at)
        VALUES (%s, %s, %s, %s, %s)
        ON CONFLICT (company_id) DO NOTHING
    """, (
        company["company_id"],
        company["name"],
        company["company_size"],
        company["industry"],
        datetime.utcnow()
    ))

conn.commit()
cur.close()
conn.close()

print(f"✅ Inserted {len(unique_companies)} companies into linkedin_companies table.")
