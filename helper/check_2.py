import psycopg2
import json
from datetime import datetime
from psycopg2.extras import RealDictCursor
import requests

import os

debug_log = []
timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
debug_file = f"updated_profiles_{timestamp}.json"


# --- CONFIG ---
PROFILE_DB_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"


# --- HELPERS ---


def fetch_company_details_with_cache(conn, company_id):
    # Try to get from cache
    cached = get_company_from_db(conn, company_id)
    if cached:
        print(f"📦 Found cached company data for ID: {company_id}")
        return {
            "company_size": cached.get("company_size"),
            "industry": cached.get("industry")
        }

    # If not cached, call API
    print(f"🌐 Fetching company data from API for ID: {company_id}")
    url = "https://fresh-linkedin-scraper-api.p.rapidapi.com/api/v1/company/profile"
    headers = {
        "x-rapidapi-key": "**************************************************",
        "x-rapidapi-host": "fresh-linkedin-scraper-api.p.rapidapi.com"
    }
    querystring = {"company_id": str(company_id)}

    try:
        response = requests.get(url, headers=headers, params=querystring, timeout=10)
        if response.status_code == 200:
            data = response.json().get("data", {})
            company_size = data.get("employee_count")
            industry = None
            if isinstance(data.get("industries"), list) and data["industries"]:
                industry = data["industries"][0]

            # Save to DB
            cur = conn.cursor()
            cur.execute("""
                INSERT INTO linkedin_companies (company_id, universal_name, name, company_size, industry)
                VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (company_id) DO NOTHING
            """, (
                company_id,
                data.get("universal_name"),
                data.get("name"),
                company_size,
                industry
            ))
            conn.commit()
            cur.close()

            return {
                "company_size": company_size,
                "industry": industry
            }

        else:
            print(f"❌ API error {response.status_code}: {response.text}")

    except Exception as e:
        print(f"❌ Exception during API call: {e}")

    return {
        "company_size": None,
        "industry": None
    }


def get_start_date(exp):
    date = exp.get("date", {})
    start = date.get("start")
    if isinstance(start, str):
        for fmt in ("%b %Y", "%B %Y"):
            try:
                return datetime.strptime(start, fmt)
            except:
                continue
        return datetime.min
    elif isinstance(start, dict):
        try:
            year = start.get("year")
            month = start.get("month", 1)
            return datetime(year, month, 1)
        except:
            return datetime.min
    return datetime.min

def is_current_job(exp):
    end = exp.get("date", {}).get("end")
    if not end:
        return True
    if isinstance(end, str) and end.lower() in ["present", "current", "ongoing"]:
        return True
    return False


def extract_company_list(experiences):
    return list({
        exp.get("company", {}).get("name")
        for exp in experiences
        if isinstance(exp, dict) and isinstance(exp.get("company"), dict) and exp.get("company", {}).get("name")
    })

def extract_school_list(educations):
    return list({
        edu["school"]
        for edu in educations
        if isinstance(edu, dict) and isinstance(edu.get("school"), str)
    })

def is_student(educations):
    now = datetime.now()

    for edu in educations:
        end = edu.get("date", {}).get("end")

        if not end:
            return True  # no end date → probably still studying

        if isinstance(end, str):
            if end.lower() in ["present", "ongoing", "current"]:
                return True
            for fmt in ("%Y", "%b %Y", "%B %Y"):
                try:
                    parsed = datetime.strptime(end, fmt)
                    if parsed > now:
                        return True  # ends in the future → still a student
                    break
                except:
                    continue

        elif isinstance(end, dict):
            year = end.get("year")
            month = end.get("month", 1)
            try:
                end_date = datetime(year, month, 1)
                if end_date > now:
                    return True
            except:
                continue

    return False

def get_graduation_year(educations):
    years = []

    for edu in educations:
        end = edu.get("date", {}).get("end")

        if isinstance(end, str):
            try:
                # Try to parse "2015" or "May 2015"
                for fmt in ("%Y", "%b %Y", "%B %Y"):
                    try:
                        parsed = datetime.strptime(end, fmt)
                        years.append(parsed.year)
                        break
                    except:
                        continue
            except:
                continue

        elif isinstance(end, dict):
            year = end.get("year")
            if isinstance(year, int):
                years.append(year)

    return max(years) if years else None


def get_highest_degree(educations):
    levels = ["doctor", "phd", "master", "bachelor", "associate"]

    # Sanitize input again just in case
    valid_educations = [e for e in educations if isinstance(e, dict)]

    sorted_educations = sorted(
        valid_educations,
        key=lambda e: (
            isinstance(e.get("date", {}).get("start"), dict)
            and e.get("date", {}).get("start", {}).get("year", 0)
        ),
        reverse=True
    )

    for edu in sorted_educations:
        degree = edu.get("degree", "").lower()
        for level in levels:
            if level in degree:
                return level
    return None

def get_company_from_db(conn, company_id):
    cur = conn.cursor(cursor_factory=RealDictCursor)
    cur.execute("SELECT * FROM linkedin_companies WHERE company_id = %s", (company_id,))
    row = cur.fetchone()
    cur.close()
    return row


def get_yoe(experiences):
    start_dates = []

    for exp in experiences:
        start = exp.get("date", {}).get("start")

        if isinstance(start, str):
            # Try to parse string formats like "Feb 2025" or "February 2025"
            for fmt in ("%b %Y", "%B %Y"):
                try:
                    parsed = datetime.strptime(start, fmt)
                    start_dates.append(parsed)
                    break
                except:
                    continue

        elif isinstance(start, dict):
            # Handle {"year": 2023, "month": 2}
            try:
                year = start.get("year")
                month = start.get("month", 1)
                parsed = datetime(year, month, 1)
                start_dates.append(parsed)
            except:
                continue

    if not start_dates:
        return 0.0

    first = min(start_dates)
    return round((datetime.now() - first).days / 365, 2)



def get_field_of_study(educations):
    for edu in reversed(educations):
        degree = edu.get("degree")
        if isinstance(degree, str) and degree.strip():
            return degree.strip()
    return None

def ensure_new_columns_exist(conn):
    cursor = conn.cursor()
    cursor.execute("""
        ALTER TABLE linkedin_profiles
        ADD COLUMN IF NOT EXISTS company_list JSONB,
        ADD COLUMN IF NOT EXISTS school_list JSONB,
        ADD COLUMN IF NOT EXISTS current_company_size TEXT,
        ADD COLUMN IF NOT EXISTS industry TEXT,
        ADD COLUMN IF NOT EXISTS is_student BOOLEAN,
        ADD COLUMN IF NOT EXISTS graduation_year INT,
        ADD COLUMN IF NOT EXISTS highest_degree TEXT,
        ADD COLUMN IF NOT EXISTS yoe FLOAT,
        ADD COLUMN IF NOT EXISTS field_of_study TEXT,
        ADD COLUMN IF NOT EXISTS current_company TEXT,
        ADD COLUMN IF NOT EXISTS current_title TEXT,
        ADD COLUMN IF NOT EXISTS current_job_duration_years FLOAT
    """)
    conn.commit()
    cursor.close()
    print("✅ Ensured all new columns exist.")


# --- MAIN ---
conn = psycopg2.connect(PROFILE_DB_URL)
ensure_new_columns_exist(conn)
cursor = conn.cursor(cursor_factory=RealDictCursor)

cursor.execute("""
    SELECT id, full_name, experiences, educations
    FROM linkedin_profiles    
""")

rows = cursor.fetchall()

for row in rows:    
    profile_id = row["id"]
    name = row["full_name"]
    print(f"\n🔍 Processing profile: {name} (ID: {profile_id})")

    raw_exp = row.get("experiences")
    raw_edu = row.get("educations")

    try:
        experiences = json.loads(raw_exp) if isinstance(raw_exp, str) else raw_exp
        experiences = [e for e in experiences if isinstance(e, dict)]
    except Exception as e:
        print(f"❌ Error parsing experiences JSON: {e}")
        experiences = []

    try:
        educations = json.loads(raw_edu) if isinstance(raw_edu, str) else raw_edu
        educations = [e for e in educations if isinstance(e, dict)]
    except Exception as e:
        print(f"❌ Error parsing educations JSON: {e}")
        educations = []


    print(f"📦 Experiences loaded: {len(experiences)}")
    print(f"🎓 Educations loaded: {len(educations)}")

    # Try to get company ID from the most recent experience
    company_id = None
    if experiences:
        company = experiences[0].get("company", {})
        company_id = company.get("id")

    if company_id:
        api_data = fetch_company_details_with_cache(conn, company_id)
    else:
        api_data = {"company_size": None, "industry": None}

    # Get most recent experience (already sorted logic, assumed)
    latest_exp = experiences[0] if experiences else None
    current_company = None
    current_title = None
    company_size = None
    industry = None    
    current_job_duration_years = None


    if latest_exp and is_current_job(latest_exp):
        company = latest_exp.get("company", {})
        current_company = latest_exp.get("company", {}).get("id")
        current_title = latest_exp.get("title")
        company_id = company.get("id")
        dt = get_start_date(latest_exp)
        current_job_duration_years = round((datetime.now() - dt).days / 365, 2) if dt != datetime.min else None

        if company_id:
            api_data = fetch_company_details_with_cache(conn, company_id)
            company_size = api_data["company_size"]
            industry = api_data["industry"]
    
    current_timestamp = datetime.utcnow()

    # --- Enrich fields ---
    enriched = {
        "company_list": extract_company_list(experiences),
        "school_list": extract_school_list(educations),
        "current_company_size": company_size,
        "industry": industry,
        "is_student": is_student(educations),
        "graduation_year": get_graduation_year(educations),
        "highest_degree": get_highest_degree(educations),
        "yoe": get_yoe(experiences),
        "field_of_study": get_field_of_study(educations),
        "current_company": current_company,
        "current_title": current_title,
        "current_job_duration_years": current_job_duration_years,
    }

    print("🧠 Enriched data preview:")
    for k, v in enriched.items():
        print(f"  - {k}: {v}")

    # --- Update DB ---
    update_cursor = conn.cursor()
    enriched["updated_date"] = current_timestamp.isoformat()
    # Skip updating current_company if it's not in the linkedin_companies table
    if current_company:
        cur = conn.cursor()
        cur.execute("SELECT 1 FROM linkedin_companies WHERE company_id = %s", (current_company,))
        exists = cur.fetchone()
        cur.close()

        if not exists:
            print(f"❌ Skipping update for profile {profile_id} due to missing company_id in linkedin_companies: {current_company}")
            continue  # skip to next profile

    update_cursor.execute("""
        UPDATE linkedin_profiles
        SET
            company_list = %s,
            school_list = %s,
            current_company_size = %s,
            industry = %s,
            is_student = %s,
            graduation_year = %s,
            highest_degree = %s,
            yoe = %s,
            field_of_study = %s,
            current_company = %s,
            current_title = %s,
            current_job_duration_years = %s,
            updated_date = %s
        WHERE id = %s
    """, (
        json.dumps(enriched["company_list"]),
        json.dumps(enriched["school_list"]),
        enriched["current_company_size"],
        enriched["industry"],
        enriched["is_student"],
        enriched["graduation_year"],
        enriched["highest_degree"],
        enriched["yoe"],
        enriched["field_of_study"],
        enriched["current_company"],
        enriched["current_title"],
        enriched["current_job_duration_years"],
        enriched["updated_date"],
        profile_id
    ))
    conn.commit()
    update_cursor.close()
    print(f"✅ Profile updated successfully: {profile_id} - {name}")
    debug_log.append({
        "id": profile_id,
        "full_name": name,
        "company_list": enriched["company_list"],
        "school_list": enriched["school_list"],
        "current_company_size": enriched["current_company_size"],
        "industry": enriched["industry"],
        "is_student": enriched["is_student"],
        "graduation_year": enriched["graduation_year"],
        "highest_degree": enriched["highest_degree"],
        "yoe": enriched["yoe"],
        "field_of_study": enriched["field_of_study"],
        "current_company": enriched["current_company"],
        "current_title": enriched["current_title"],
        "current_job_duration_years": enriched["current_job_duration_years"],
        "updated_date": enriched["updated_date"]
    })
    try:
        with open(debug_file, "w", encoding="utf-8") as f:
            json.dump(debug_log, f, ensure_ascii=False, indent=2)
        print(f"📁 Partial debug log updated → {debug_file}")
    except Exception as e:
        print(f"⚠️ Failed to write debug file: {e}")


cursor.close()
conn.close()
