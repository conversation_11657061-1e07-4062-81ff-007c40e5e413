import psycopg2
import json
from psycopg2.extras import RealDictCursor
from datetime import datetime

# --- CONFIG ---
PROFILE_DB_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

# --- Connect DB ---
conn = psycopg2.connect(PROFILE_DB_URL)
cur = conn.cursor(cursor_factory=RealDictCursor)

# --- Step 1: Load all linkedin_profiles (with experiences) ---
cur.execute("SELECT id, experiences FROM linkedin_profiles")
profiles = cur.fetchall()

update_count = 0

for profile in profiles:
    profile_id = profile["id"]
    experiences_raw = profile.get("experiences")

    try:
        experiences = json.loads(experiences_raw) if isinstance(experiences_raw, str) else experiences_raw
        experiences = [e for e in experiences if isinstance(e, dict)]
    except Exception as e:
        print(f"⚠️ Failed to parse experiences for ID {profile_id}: {e}")
        continue

    if not experiences:
        continue

    # Try to find the current job (no end date or "present")
    current_company_name = None
    for exp in experiences:
        end = exp.get("date", {}).get("end")
        if not end or (isinstance(end, str) and end.strip().lower() in ["present", "current", "ongoing"]):
            current_company_name = exp.get("company", {}).get("name", "").strip()
            break

    if not current_company_name:
        continue

    # --- Step 2: Look up in linkedin_companies ---
    cur.execute("""
        SELECT company_size, industry FROM linkedin_companies
        WHERE LOWER(TRIM(name)) = LOWER(TRIM(%s))
        LIMIT 1
    """, (current_company_name,))
    company = cur.fetchone()

    if not company:
        print(f"❌ No match for company: {current_company_name}")
        continue

    # --- Step 3: Update profile ---
    cur.execute("""
        UPDATE linkedin_profiles
        SET current_company_size = %s,
            industry = %s,
            updated_date = %s
        WHERE id = %s
    """, (
        company.get("company_size"),
        company.get("industry"),
        datetime.utcnow(),
        profile_id
    ))
    conn.commit()
    update_count += 1
    print(f"✅ Updated profile ID {profile_id} with company '{current_company_name}'")

cur.close()
conn.close()

print(f"\n🏁 Done. Updated {update_count} profiles with company metadata.")
