import os
import json
import time
import psycopg2
import requests
from datetime import datetime
from psycopg2.extras import RealDictCursor

# --- CONFIG ---
PROFILE_DB_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

API_URL = "https://fresh-linkedin-scraper-api.p.rapidapi.com/api/v1/company/profile"
API_HEADERS = {
    "x-rapidapi-key": "**************************************************",
    "x-rapidapi-host": "fresh-linkedin-scraper-api.p.rapidapi.com"
}

timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
debug_file = f"debug_company_update_{timestamp}.json"
debug_log = []

# --- Connect DB ---
conn = psycopg2.connect(PROFILE_DB_URL)
cur = conn.cursor(cursor_factory=RealDictCursor)

# --- Get companies with missing data ---
cur.execute("""
    SELECT company_id, name FROM linkedin_companies
    WHERE company_size IS NULL OR industry IS NULL     
""")
rows = cur.fetchall()

if not rows:
    print("✅ No companies with missing metadata found.")
    exit()

print(f"🔍 Found {len(rows)} companies with missing data.")

# --- Process each row ---
for row in rows:
    company_id = row["company_id"]
    company_name = row["name"]
    result = {
        "company_id": company_id,
        "name": company_name,
        "status": "SKIPPED",
        "company_size": None,
        "industry": None,
        "universal_name": None,
        "error": None
    }

    print(f"\n🌐 Fetching: {company_name} (ID: {company_id})")

    try:
        response = requests.get(API_URL, headers=API_HEADERS, params={"company_id": company_id}, timeout=10)
        if response.status_code != 200:
            result["status"] = "API_ERROR"
            result["error"] = f"{response.status_code}: {response.text}"
            print(f"❌ API error: {response.status_code}")
            debug_log.append(result)
            continue

        data = response.json().get("data", {})
        universal_name = data.get("universal_name")
        company_size = data.get("employee_count")
        industry = None
        if isinstance(data.get("industries"), list) and data["industries"]:
            industry = data["industries"][0]

        print(f"✅ API → size={company_size}, industry={industry}, universal_name={universal_name}")

        cur.execute("""
            UPDATE linkedin_companies
            SET company_size = %s,
                industry = %s,
                universal_name = %s,
                updated_at = %s
            WHERE company_id = %s
        """, (
            str(company_size) if company_size else None,
            industry,
            universal_name,
            datetime.utcnow(),
            company_id
        ))
        conn.commit()

        result.update({
            "status": "UPDATED",
            "company_size": company_size,
            "industry": industry,
            "universal_name": universal_name
        })

    except Exception as e:
        print(f"❌ Exception: {e}")
        result["status"] = "EXCEPTION"
        result["error"] = str(e)

    debug_log.append(result)
    time.sleep(1)

cur.close()
conn.close()

# --- Write debug log ---
with open(debug_file, "w", encoding="utf-8") as f:
    json.dump(debug_log, f, ensure_ascii=False, indent=2)

print(f"\n📦 Debug log saved → {debug_file}")
print("🏁 Finished updating company metadata.")
