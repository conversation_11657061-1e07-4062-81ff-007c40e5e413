import os
import json
import psycopg2
import openai
from psycopg2.extras import RealDictCursor
from datetime import datetime
import time

# --- CONFIG ---
openai.api_key = os.getenv("********************************************************************************************************************************************************************")
PROFILE_DB_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

SYSTEM_PROMPT = """
You are a company data classifier. Given a company name, guess its approximate number of employees and industry based on the name alone remember to not use range but a definite number.

Respond ONLY in strict JSON format like:
{
  "employee_count": "value",
  "industry": "value"
}
Only return the JSON object and nothing else.
"""

# --- Connect to DB ---
conn = psycopg2.connect(PROFILE_DB_URL)
cur = conn.cursor(cursor_factory=RealDictCursor)

# --- Fetch companies with missing data and no universal_name (to avoid re-checking already filled) ---
cur.execute("""
    SELECT company_id, name
    FROM linkedin_companies
    WHERE (company_size IS NULL OR industry IS NULL)
      AND universal_name IS NULL    
""")
rows = cur.fetchall()

if not rows:
    print("✅ No companies with missing data.")
    exit()

# --- Start GPT enrichment ---
for row in rows:
    company_id = row["company_id"]
    name = row["name"]
    print(f"\n🔍 Asking GPT about: {name} (ID: {company_id})")

    user_prompt = f"Company: \"{name}\""

    try:
        response = openai.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT.strip()},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0.2
        )

        answer = response.choices[0].message.content.strip()

        try:
            parsed = json.loads(answer)
            employee_count = parsed.get("employee_count")
            industry = parsed.get("industry")

            print(f"✅ GPT response: size={employee_count}, industry={industry}")

            # --- Update database ---
            cur.execute("""
                UPDATE linkedin_companies
                SET company_size = %s,
                    industry = %s,
                    updated_at = %s
                WHERE company_id = %s
            """, (
                employee_count,
                industry,
                datetime.utcnow(),
                company_id
            ))
            conn.commit()

        except Exception as e:
            print(f"⚠️ Failed to parse GPT JSON: {answer}\nError: {e}")

    except Exception as e:
        print(f"❌ GPT API error: {e}")

    time.sleep(1.5)  # prevent OpenAI rate limit

cur.close()
conn.close()
print("\n🏁 Done enriching missing company data using OpenAI.")
