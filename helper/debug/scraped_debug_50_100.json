[{"id": "1177987709", "urn": "ACoAAEY2qn0BaAsFZexfco5UlGfs6tWtQC_3GI4", "public_identifier": "sudarsan-reddy✅-a2560928a", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Reddy✅", "full_name": "<PERSON><PERSON><PERSON> Reddy✅", "headline": "Lead Sales Recruiter", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": 23, "month": 12, "year": null}, "pronoun": null, "created": 1693205266885, "created_date": "2023-08-28T06:47:46.885Z", "location": {"country": "India", "country_code": "IN", "city": "Hyderabad, Telangana, India", "postal_code": null}, "avatar": [{"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D5603AQGJV_OdnJFntA/profile-displayphoto-shrink_800_800/B56ZXMHfnNGsAc-/0/1742886272739?e=**********&v=beta&t=aRsDKFuEuSav_aJaA6WliOWvRCsKBLnOlg2NjZNVHnU", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D5603AQGJV_OdnJFntA/profile-displayphoto-shrink_400_400/B56ZXMHfnNGsAg-/0/1742886272739?e=**********&v=beta&t=ZLEEvs6Sx31_J2dKN44HQZK53bF8yHNnDiHB_wK2Q3U", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D5603AQGJV_OdnJFntA/profile-displayphoto-shrink_100_100/B56ZXMHfnNGsAU-/0/1742886272739?e=**********&v=beta&t=Q5HfiKC3QNnBkcTzWTTBHvam4akj1PvPFdpjAxVXhOQ", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5603AQGJV_OdnJFntA/profile-displayphoto-shrink_200_200/B56ZXMHfnNGsAY-/0/1742886272745?e=**********&v=beta&t=jd_F_B4Iep3zZsHM3YudtjrdpWztZCeHmW83Jdy96Xk", "expires_at": **********000}], "cover": [{"width": 800, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5616AQFfWg70vpLkCQ/profile-displaybackgroundimage-shrink_200_800/profile-displaybackgroundimage-shrink_200_800/0/1731693443684?e=**********&v=beta&t=d8DBSsNItX5TQFEBSOBShZ62XQPbqfRVHq6HedDFTNo", "expires_at": **********000}, {"width": 1400, "height": 350, "url": "https://media.licdn.com/dms/image/v2/D5616AQFfWg70vpLkCQ/profile-displaybackgroundimage-shrink_350_1400/profile-displaybackgroundimage-shrink_350_1400/0/1731693443684?e=**********&v=beta&t=VRtR8aKUCPAll8aarC_tWKUNIHq9iiad16DvMBapHQo", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Lead Sales Recruiter", "location": "Monmouth Junction, New Jersey, United States · Remote", "date": {"start": "Sep 2023", "end": "Present"}, "employment_type": "Full-time", "skills": ["Proficient in managing high-performing recruitment teams, developing effective marketing strategies for bench consultants, and building strong relationships with clients, vendors, and Tier-1 partners. Adept at overseeing the full recruitment lifecycle, from consultant onboarding to successful placement, while ensuring compliance with visa regulations and industry standards. Skilled in market analysis, negotiation, and leveraging job boards and social media to maximize outreach. Passionate about driving team success and contributing to organizational growth through strategic talent acquisition."], "company": {"id": "11007887", "name": "Arohak Inc.", "url": "https://www.linkedin.com/company/11007887/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGaWkUkD-14Yg/company-logo_200_200/company-logo_200_200/0/1630606792830?e=**********&v=beta&t=yZM1JM5K4BTuTylMvzn8jn2UhvcOnM_Xl4M5ooloNcM", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGaWkUkD-14Yg/company-logo_100_100/company-logo_100_100/0/1630606792830?e=**********&v=beta&t=k_9OrZPRNmZOsYN1YgiRlK6dY-2BPR_j4KNp54S5ljs", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGaWkUkD-14Yg/company-logo_400_400/company-logo_400_400/0/1630606792830?e=**********&v=beta&t=QqRRSC3tcZOIr6nFyKD8rN6W8btGwt_6YMNt0bWZwhQ", "expires_at": **********000}]}}, {"title": "Bench Sales Recruiter", "description": "I was involved in actively sourcing new client opportunities, maintaining relationships with both clients and candidates, negotiating contracts, and staying informed about market trends.", "location": "New Jersey, United States · Remote", "date": {"start": "Mar 2023", "end": "Sep 2023"}, "employment_type": "Full-time", "skills": ["Sales and Business Development", "Recruitment and Networking", "Negotiation", "Market Knowledge"], "company": {"id": "104935", "name": "Logic Planet", "url": "https://www.linkedin.com/company/104935/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQG-lHCxIfv8jQ/company-logo_200_200/company-logo_200_200/0/1704708022587/logic_planet_logo?e=**********&v=beta&t=k5Yk98uuMRzWT8Bd9DPxD_-VEXrqkmSKJwl0TR9uYho", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQG-lHCxIfv8jQ/company-logo_100_100/company-logo_100_100/0/1704708022587/logic_planet_logo?e=**********&v=beta&t=7S9O3perMebDtT_RaqMK51KeyEW7BAM42bvwg5mGPXI", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQG-lHCxIfv8jQ/company-logo_400_400/company-logo_400_400/0/1704708022587/logic_planet_logo?e=**********&v=beta&t=ofWha9vd55LRNEIDFpWIEnH3xSbxyqL93YzDT99M4sY", "expires_at": **********000}]}}], "skills": [{"skill": "Time Management"}, {"skill": "Sourcing"}, {"skill": "Sales and Business Development"}, {"skill": "Recruitment and Networking"}, {"skill": "Negotiation"}, {"skill": "Market Knowledge"}, {"skill": "Recruiting"}, {"skill": "Employee Relations"}], "certifications": [], "publications": [], "educations": [{"school": "Qis College of Engineering and Technology", "date": {"start": "Aug 2019", "end": "Apr 2023"}, "degree": "Bachelor of Technology - BTech, Civil Engineering"}], "honors": [], "volunteers": []}, {"id": "*********", "urn": "ACoAACzjywMBtc_VKjk2UvbuowCMibUriQ_6nIE", "public_identifier": "jyothi-a-a83278190", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "A.", "full_name": "<PERSON><PERSON><PERSON>.", "headline": "Senior Resource Coordinator & Lead Talent Acquisition Specialist | \nIf you're seeking a new employer, we have numerous direct client opportunities available, including H1B transfers and immediate green card processing!", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1563884302207, "created_date": "2019-07-23T12:18:22.207Z", "location": {"country": "United States", "country_code": "US", "city": "Phoenix, Arizona, United States", "postal_code": null}, "avatar": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5603AQFor0wp7z7AbA/profile-displayphoto-shrink_200_200/B56ZOR2Ke_G8AY-/0/1733318715757?e=**********&v=beta&t=CmQu1qOPnaEwLa264tEumqGCbioIrY02SDHDy16aieM", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5603AQFor0wp7z7AbA/profile-displayphoto-shrink_800_800/B56ZOR2Ke_G8Ac-/0/1733318715757?e=**********&v=beta&t=bjcxbieTEYmO9vmzbjRgz8b8mCU3Oc34Wbxisb4h-jY", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D5603AQFor0wp7z7AbA/profile-displayphoto-shrink_100_100/B56ZOR2Ke_G8AU-/0/1733318715757?e=**********&v=beta&t=OwYPIEm42ewhMgy-Kii6Xag9vy903e1Hoi_c5_W3uPk", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5603AQFor0wp7z7AbA/profile-displayphoto-shrink_400_400/B56ZOR2Ke_G8Ag-/0/1733318715758?e=**********&v=beta&t=YzkH2Qtte2P6zXQWwyBXIpXwkshT2CVmeNPhdImc9kw", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Senior Resource Coordinator & Lead Talent Acquisition Specialist", "location": "Phoenix, Arizona, United States", "date": {"start": "Jun 2021", "end": "Present"}, "employment_type": "Full-time", "company": {"id": "3242693", "name": "HUMAC INC", "url": "https://www.linkedin.com/company/3242693/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFEudsvoOojVg/company-logo_200_200/company-logo_200_200/0/1631324447784?e=**********&v=beta&t=Ok3yg_AMfuNYc9upFduS20-6SEuElLmjftXOI7lHTVs", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFEudsvoOojVg/company-logo_100_100/company-logo_100_100/0/1631324447784?e=**********&v=beta&t=UfH-PlF_lPONo-F9oXzdt5v-UESiKlwi-tCcJuQxbdo", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFEudsvoOojVg/company-logo_400_400/company-logo_400_400/0/1631324447784?e=**********&v=beta&t=-zkajbDSemDUWPFFPoSFwIYe_n5sya6GN8_KbS5wh7U", "expires_at": **********000}]}}, {"title": "Talent Acquisition Specialist", "location": "Dallas-Fort Worth Metroplex", "date": {"start": "Sep 2019", "end": "Jun 2021"}, "company": {"id": "3686136", "name": "TSQ Systems Inc", "url": "https://www.linkedin.com/company/3686136/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C510BAQGrLCHLFfLy9Q/company-logo_200_200/company-logo_200_200/0/1631391991475?e=**********&v=beta&t=IIpIy_43ya8KI5d-y5DTgpyS4tJcBCAq0eOkiOMfsYc", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C510BAQGrLCHLFfLy9Q/company-logo_100_100/company-logo_100_100/0/1631391991475?e=**********&v=beta&t=tKOPAsCOZQ2Xl3jQCRYpK1XtmC4a2oY2-0IzOFVJjG0", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C510BAQGrLCHLFfLy9Q/company-logo_400_400/company-logo_400_400/0/1631391991475?e=**********&v=beta&t=2U72cdcKHiwWKxCx0l7-SLbn1uA3WnNikg3fMf3pcTo", "expires_at": **********000}]}}, {"title": "IT Recruiter", "location": "Dallas-Fort Worth Metroplex", "date": {"start": "Aug 2018", "end": "Sep 2019"}, "company": {"id": "3804356", "name": "Cloud BigData Technologies Group", "url": "https://www.linkedin.com/company/3804356/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQEAI9V-sQoZcw/company-logo_200_200/company-logo_200_200/0/1631336759551?e=**********&v=beta&t=wL43lp5UvVrDlGsWn4OI7G-CCua97ny7jAcLwdM_mqw", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQEAI9V-sQoZcw/company-logo_100_100/company-logo_100_100/0/1631336759551?e=**********&v=beta&t=KVPIEVggUngWS8HBKv8qy2KfPsgKBnYBY8gmuw-gP2I", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQEAI9V-sQoZcw/company-logo_400_400/company-logo_400_400/0/1631336759551?e=**********&v=beta&t=I5LISipCUrxfwZyMDQ22OZXhaosVNGl7Icl1b81P7c8", "expires_at": **********000}]}}], "skills": [{"skill": "Embedded Systems"}, {"skill": "Embedded Software"}, {"skill": "IT Recruitment"}, {"skill": "Internet of Things (IoT)", "num_endorsements": 1}, {"skill": "C (Programming Language)", "num_endorsements": 1}], "certifications": [], "publications": [], "educations": [{"school": "V.K.R, V.N.B & A.G.K College of Engineering", "date": {"start": "2014", "end": "2018"}, "degree": "Bachelor's degree,  Electronics and Communications Engineering", "grade": "Distinction "}], "honors": [], "volunteers": []}, {"id": "*********", "urn": "ACoAADXScKMBaqgdNluiWktaCfnbMfqDQtZ3HAw", "public_identifier": "meghana-deshmukh-1174a8212", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "full_name": "<PERSON><PERSON>", "headline": "Senior Technical Recruiter", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1621334644102, "created_date": "2021-05-18T10:44:04.102Z", "location": {"country": "United States", "country_code": "US", "city": "Jacksonville, Florida, United States", "postal_code": null}, "avatar": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5603AQGnQnZjwDbHpg/profile-displayphoto-shrink_200_200/B56ZbLUlqMGsAc-/0/1747167894860?e=**********&v=beta&t=T7BlLHbMcBpRiAp_eBwj2Q-PtfQfiK44ZbdHkrvzg0o", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D5603AQGnQnZjwDbHpg/profile-displayphoto-shrink_400_400/B56ZbLUlqMGsAk-/0/1747167894860?e=**********&v=beta&t=eY1J1dNZ0nXHYxm2LPjxhVe1mgYttws77VD1IKPiJH8", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D5603AQGnQnZjwDbHpg/profile-displayphoto-shrink_100_100/B56ZbLUlqMGsAY-/0/1747167894860?e=**********&v=beta&t=Yc2qY9hsLtK2JFEJHc98i7ApUvPxpM2jKoVD9FSiMiY", "expires_at": **********000}, {"width": 513, "height": 513, "url": "https://media.licdn.com/dms/image/v2/D5603AQGnQnZjwDbHpg/profile-displayphoto-shrink_800_800/B56ZbLUlqMGsAg-/0/1747167894865?e=**********&v=beta&t=wmYvGN3Kb5-iMdkvXqAXx0hZGfFtgCWMrVTyvrn0CD4", "expires_at": **********000}], "cover": [{"width": 800, "height": 199, "url": "https://media.licdn.com/dms/image/v2/D5616AQHnrc9sD6V0lg/profile-displaybackgroundimage-shrink_200_800/profile-displaybackgroundimage-shrink_200_800/0/1736543231013?e=**********&v=beta&t=TkrTYs02kUSfJ1HnfPKYPHxStv9e0OYQ6JNbXOh-eRw", "expires_at": **********000}, {"width": 1118, "height": 279, "url": "https://media.licdn.com/dms/image/v2/D5616AQHnrc9sD6V0lg/profile-displaybackgroundimage-shrink_350_1400/profile-displaybackgroundimage-shrink_350_1400/0/1736543231013?e=**********&v=beta&t=W4_450kN507twugH5-k5OHTRXm0v0mQ_3oGgKDbQHSM", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Senior Technical Recruiter", "description": "Skills: Recruitment-to-Recruitment · IT Recruitment · Technology Recruitment · Internal Recruitment", "location": "Virginia, United States", "date": {"start": "May 2025", "end": "Present"}, "employment_type": "Full-time", "company": {"id": "2628080", "name": "Conquer Technologies", "url": "https://www.linkedin.com/company/2628080/", "logo": [{"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQFAqkPLmuK3YQ/company-logo_400_400/B56Za1hUgcGgAY-/0/1746802133436/conquer_technologies_logo?e=**********&v=beta&t=hP369eqXV1jSTjPSzPFbsKi0IFyxbcmdiuUQHlxgHpk", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQFAqkPLmuK3YQ/company-logo_200_200/B56Za1hUgcGgAI-/0/1746802133436/conquer_technologies_logo?e=**********&v=beta&t=hwsT47fHvdn3Pl399ouzjeyzhsgK0-pmNYtfJjtSbN0", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQFAqkPLmuK3YQ/company-logo_100_100/B56Za1hUgcGgAQ-/0/1746802133436/conquer_technologies_logo?e=**********&v=beta&t=JKKs3jNxfLVCCirUuZstMjAbl7-Ey1JFaMc9qn6d8S4", "expires_at": **********000}]}}, {"title": "Sr. US IT/ Sales Recruiter", "location": "Jacksonville ,FLorida 32216 · On-site", "date": {"start": "May 2024", "end": "May 2025"}, "employment_type": "Full-time", "company": {"id": "*********", "name": "Tek Artisans LLC", "url": "https://www.linkedin.com/company/*********/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQGysF-aauAeGA/company-logo_200_200/company-logo_200_200/0/1714138459515/tek_artisans_llc_logo?e=**********&v=beta&t=8RDxh8XxfvjCOFuyoeXzX_vCzKOENRNPC8dxsXnLyKM", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQGysF-aauAeGA/company-logo_100_100/company-logo_100_100/0/1714138459515/tek_artisans_llc_logo?e=**********&v=beta&t=z6pYSy1PGVVJRNWab-7VIEv7Zo6-M3VInjRMSgC1vHg", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQGysF-aauAeGA/company-logo_400_400/company-logo_400_400/0/1714138459515/tek_artisans_llc_logo?e=**********&v=beta&t=UdCc7DZzy-CW2p5i-N2qqKKZj0xfoLRS8nFDf7uCtvY", "expires_at": **********000}]}}, {"title": "US IT Bench Sales Recruiter", "description": "Skills: Recruiting · Screening · Sales Management · Salary Negotiations · Vendor Management · tier 1 · Tier II", "location": "Hyderabad, Telangana, India · On-site", "date": {"start": "Feb 2022", "end": "Mar 2024"}, "employment_type": "Full-time", "company": {"id": "2922575", "name": "Denken Solutions, Inc.", "url": "https://www.linkedin.com/company/2922575/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQEYUPdFIH7MtA/company-logo_200_200/company-logo_200_200/0/1688465693669/denkensolutions_logo?e=**********&v=beta&t=FOt3VAotIj7I2y3s-NmMo9sphgRNf0z-lUL34yKHqx0", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQEYUPdFIH7MtA/company-logo_100_100/company-logo_100_100/0/1688465693669/denkensolutions_logo?e=**********&v=beta&t=GO2k7cMggjST5ZlyfzXQe58Kgw9g6Qb9yHRDbRpyNDw", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQEYUPdFIH7MtA/company-logo_400_400/company-logo_400_400/0/1688465693669/denkensolutions_logo?e=**********&v=beta&t=3GQNwZyW148lXIm5wqA8YtNdK7pRDEueKXmg17oOeZY", "expires_at": **********000}]}}], "skills": [{"skill": "Recruitment-to-Recruitment"}, {"skill": "Technology Recruitment"}, {"skill": "Internal Recruitment"}, {"skill": "Salary Negotiations"}, {"skill": "Vendor Management"}, {"skill": "tier 1"}, {"skill": "Tier II"}, {"skill": "Sales", "num_endorsements": 1}, {"skill": "Executive Search", "num_endorsements": 1}, {"skill": "Cold Calling"}, {"skill": "Full Life Cycle Recruiting"}, {"skill": "IT Recruitment"}, {"skill": "Recruiting"}, {"skill": "Screening"}, {"skill": "Sales Management"}], "certifications": [], "publications": [], "educations": [], "honors": [], "volunteers": []}, {"id": "*********", "urn": "ACoAACd2BBkBglNeYvfGiPOWGJ6hkGZqOvmcXpM", "public_identifier": "sravan-kumar-maddy-*********", "first_name": "<PERSON>avan", "last_name": "<PERSON> ( Maddy )", "full_name": "<PERSON><PERSON> ( Maddy )", "headline": "Lead Bench Sales Recruiter at Siri Data Analytics", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1527606415621, "created_date": "2018-05-29T15:06:55.621Z", "location": {"country": "India", "country_code": "IN", "city": "Hyderabad, Telangana, India", "postal_code": null}, "avatar": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D03AQG3iOWcD-9W-w/profile-displayphoto-shrink_800_800/B4DZONF4yAHUAc-/0/1733238951511?e=**********&v=beta&t=hQCdK6Eew7SKuLuaKyORZUK6tLkaCnbddJBMnjL-lsA", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D03AQG3iOWcD-9W-w/profile-displayphoto-shrink_200_200/B4DZONF4yAHUAY-/0/1733238951511?e=**********&v=beta&t=c5Axmrp_E92JcP-YnjCo3V_UpbIc1fuT2TzblgJs4IU", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D4D03AQG3iOWcD-9W-w/profile-displayphoto-shrink_100_100/B4DZONF4yAHUAU-/0/1733238951511?e=**********&v=beta&t=gzE5ZUipXc-VfQAfRTt2M0rDEFVKaGDAkob9wdbv2EQ", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D03AQG3iOWcD-9W-w/profile-displayphoto-shrink_400_400/B4DZONF4yAHUAg-/0/1733238951517?e=**********&v=beta&t=DFIBQ_uVx-E8Bet8YV2QE_EmQFDzZNJ2Qo3PDe63XdM", "expires_at": **********000}], "cover": [{"width": 800, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C5116AQFQW7ZVoTt9aQ/profile-displaybackgroundimage-shrink_200_800/profile-displaybackgroundimage-shrink_200_800/0/1540568459272?e=**********&v=beta&t=Nc4Mz4mIAqWQh34pIqsJoqke5gxuEJVOku3EBlXoY80", "expires_at": **********000}, {"width": 1400, "height": 350, "url": "https://media.licdn.com/dms/image/v2/C5116AQFQW7ZVoTt9aQ/profile-displaybackgroundimage-shrink_350_1400/profile-displaybackgroundimage-shrink_350_1400/0/1540568459777?e=**********&v=beta&t=dUMEasZheM1YenA79-ReuoKtT9dfhID6DTZJO82cmVw", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Senior Sales Executive", "location": "United States · On-site", "date": {"start": "May 2024", "end": "Present"}, "employment_type": "Full-time", "company": {"id": "96660844", "name": "Siri Data Analytics", "url": "https://www.linkedin.com/company/96660844/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQEjH9s23LNS2w/company-logo_200_200/company-logo_200_200/0/1692998680948/siri_data_analytics_llc_logo?e=**********&v=beta&t=QdsalYRo2DhOnOFzAiDdeM7O-dnFzfq3cnFTdsMoe5Q", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQEjH9s23LNS2w/company-logo_100_100/company-logo_100_100/0/1692998680948/siri_data_analytics_llc_logo?e=**********&v=beta&t=_dlDCsBBLa8huA930HMTz6UyRbx4Cb2LwB_MqAUdJxc", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQEjH9s23LNS2w/company-logo_400_400/company-logo_400_400/0/1692998680948/siri_data_analytics_llc_logo?e=**********&v=beta&t=2TFcK27KVA1juzqJWlOSdayYu7NW0hJESAuqiUECCmY", "expires_at": **********000}]}}, {"title": "Senior Sales Executive", "date": {"start": "Aug 2022", "end": "Apr 2024"}, "employment_type": "Full-time", "company": {"id": "14485943", "name": "Involgix Inc", "url": "https://www.linkedin.com/company/14485943/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C510BAQHk5q8geZ0xVQ/company-logo_200_200/company-logo_200_200/0/1630629576339/involgix_logo?e=**********&v=beta&t=VP0QH7jWm6IiYFuaHZOjZ2q_Nt3bX5K8qCVxD5WfHio", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C510BAQHk5q8geZ0xVQ/company-logo_100_100/company-logo_100_100/0/1630629576340/involgix_logo?e=**********&v=beta&t=SM44jURIcD_yXOSIqzXsYNrMmBUwruDILb-cqQCqQPM", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C510BAQHk5q8geZ0xVQ/company-logo_400_400/company-logo_400_400/0/1630629576340/involgix_logo?e=**********&v=beta&t=ycbkZ2dMFkgj3bIj8BqLqtPxR2LdCKSUDZO8f0pn6r0", "expires_at": **********000}]}}, {"title": "Senior Sales Executive", "date": {"start": "Jun 2021", "end": "Jul 2022"}, "company": {"id": "76087721", "name": "Trafodion", "url": "https://www.linkedin.com/company/76087721/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGss3sFSTk7QA/company-logo_200_200/company-logo_200_200/0/1630479939855/trafodion_logo?e=**********&v=beta&t=6sHUwGsgNX3DyYaTdq6nkFOUpFG27NEQkd5IH2O8yXc", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGss3sFSTk7QA/company-logo_100_100/company-logo_100_100/0/1630479939855/trafodion_logo?e=**********&v=beta&t=xJc4B3j2CPRHnm7Hr_S1HU21Gd1N4AcUoXGkLfwftjc", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGss3sFSTk7QA/company-logo_400_400/company-logo_400_400/0/1630479939855/trafodion_logo?e=**********&v=beta&t=pc5fAvwMbLdIPvBNqsnLFWU0DeTbvkSDQ0HlgnQi38k", "expires_at": **********000}]}}, {"title": "Senior Sales Executive", "date": {"start": "Dec 2020", "end": "Jul 2021"}, "employment_type": "Full-time", "company": {"id": "7586071", "name": "TEK NINJAS", "url": "https://www.linkedin.com/company/7586071/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C560BAQF1cDsZLFyuFw/company-logo_200_200/company-logo_200_200/0/1678197955370?e=**********&v=beta&t=8slZr_SN_wlnyzFsrpQUqedNVss18IPdDGo-JfhFozc", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C560BAQF1cDsZLFyuFw/company-logo_100_100/company-logo_100_100/0/1678197955370?e=**********&v=beta&t=V75iE1AXKh8c34U9IkOf3PS0JBoe7AmncdlcDW6o_cQ", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C560BAQF1cDsZLFyuFw/company-logo_400_400/company-logo_400_400/0/1678197955371?e=**********&v=beta&t=2uZguHNAytY_c1zcd3exaFBsCg-Sk8kJHClVLoUYEb8", "expires_at": **********000}]}}, {"title": "Bench sales recruiter", "date": {"start": "Nov 2017", "end": "Nov 2020"}, "company": {"id": "22314214", "name": "Elite IT Professionals Inc", "url": "https://www.linkedin.com/company/22314214/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGLUJK_5wRQ0Q/company-logo_200_200/company-logo_200_200/0/1631331351165?e=**********&v=beta&t=JwaR7_-cauzbbBBoSdrJqxwlfMijux93tnoULq5tsgc", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGLUJK_5wRQ0Q/company-logo_100_100/company-logo_100_100/0/1631331351165?e=**********&v=beta&t=np-A2wqPlRFy6qwRLlJYj7NxNtm1RAZSS1NSvn4nNrI", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGLUJK_5wRQ0Q/company-logo_400_400/company-logo_400_400/0/1631331351165?e=**********&v=beta&t=I0dGnV93mgCGQIGXcm7gXEdMlAYT51CsNq-RmuwAxaM", "expires_at": **********000}]}}, {"title": "Jr. <PERSON>ch Sales Recruiter", "date": {"start": "May 2017", "end": "Oct 2017"}, "employment_type": "Full-time", "company": {"id": "3622216", "name": "Renee Systems Inc", "url": "https://www.linkedin.com/company/3622216/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGxXbDT4h-Qog/company-logo_200_200/company-logo_200_200/0/1631341478893?e=**********&v=beta&t=cOu56I-ZxwNNjWLkEyH6j-Ms8ARhxBGuh8AwwFV8RX8", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGxXbDT4h-Qog/company-logo_100_100/company-logo_100_100/0/1631341478893?e=**********&v=beta&t=DjhOPiqt4yzIm6q9G2PsGlgRypvlR_WuX9DSycTJiqk", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGxXbDT4h-Qog/company-logo_400_400/company-logo_400_400/0/1631341478893?e=**********&v=beta&t=f3wBBbnfa6b8J-1Uoq5wbqyZ8Z1QAF0HhSdlhEe6G-g", "expires_at": **********000}]}}], "skills": [{"skill": "Technical Recruiting", "num_endorsements": 1}, {"skill": "Benefits Negotiation"}, {"skill": "Screening Resumes"}, {"skill": "Contract Recruitment"}, {"skill": "Screening"}, {"skill": "Sourcing"}, {"skill": "Management"}], "certifications": [], "publications": [], "educations": [{"school": "Ganapathi Engineering College", "date": {"start": "2013", "end": "2017"}, "degree": "Bachelor of Engineering - BE"}, {"school": "Wisdom High School", "date": {"start": "2011", "end": "2012"}}], "honors": [], "volunteers": []}, {"id": "1039615123", "urn": "ACoAAD33RJMB17b4iMm8hTqmu-znFl_AnZ6eMIg", "public_identifier": "moham<PERSON>-samad-1571b9250", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "full_name": "<PERSON>", "headline": "Bench Sales Recruiter", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1663009491182, "created_date": "2022-09-12T19:04:51.182Z", "location": {"country": "United States", "country_code": "US", "city": "Itasca, Illinois, United States", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D5603AQGG-uyHlAiDhw/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/1665166327263?e=**********&v=beta&t=zp2GgeEdYuntzm3Y7dhaGXsSK0_Wluijix6Bu1Y3ejE", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5603AQGG-uyHlAiDhw/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1665166327263?e=**********&v=beta&t=ahNauJFoCFGB-32lM9XDNOO-_iJ0LvOJsxQMflM_F0s", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D5603AQGG-uyHlAiDhw/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1665166327263?e=**********&v=beta&t=Y-UNyN-nk49jN-mF1H8-ymHDp6C5Jt6tjPtT4-0HqKM", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D5603AQGG-uyHlAiDhw/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1665166327263?e=**********&v=beta&t=2ZUguxXbDTHAkF4jji8CtxqUkaMuYUfZorNGe0oZYNM", "expires_at": **********000}], "cover": [{"width": 744, "height": 186, "url": "https://media.licdn.com/dms/image/v2/D4E16AQFmgdog1krkkA/profile-displaybackgroundimage-shrink_200_800/profile-displaybackgroundimage-shrink_200_800/0/1702490888893?e=**********&v=beta&t=xPSE2h1bNJPLCDDjv8I3KQWIbizhVbaJjPZ30S66JDs", "expires_at": **********000}, {"width": 744, "height": 186, "url": "https://media.licdn.com/dms/image/v2/D4E16AQFmgdog1krkkA/profile-displaybackgroundimage-shrink_350_1400/profile-displaybackgroundimage-shrink_350_1400/0/1702490888893?e=**********&v=beta&t=3Rw9WACIqNuC7MoMaxnT0N-6T0uSkPFsH_Uf228tieg", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Bench Sales Recruiter", "description": "Identified potential job openings in the IT industry and reached out to potential clients to discuss the job \nopenings and the qualifications of the IT professionals on the bench. \n• Screened IT professionals on the bench to ensure that they have the necessary skills and experience for the \njob openings. \n• Marketed candidates to potential clients and negotiated rates to ensure that they are competitive and fair. \n• Built and maintained strong relationships with both clients and IT professionals on the bench to ensure long\u0002term success. \n• Maintained accurate records of all job openings, candidates, and clients to ensure that everything is organized \nand easy to access. \n• Stayed up-to-date on industry trends and changes in the job market to provide the best service possible to \nclients and candidates. \n• Involved in sharing hotlist to potential vendors through email. \n• Building data base on daily basis by commenting over LinkedIn job posts or personal messages. \n• Submit the candidates for the suitable positions and following up regarding the rates and client interviews.\n• Connecting with hiring managers and Tech recruiters on regular basis \n• Updated or making changes in resumes as per the requirements \n• Achieved sales/submission targets on daily and monthly basis \n• Good knowledge on Technologies \n• Dealt with minimum 5 resumes at a time \n• Successfully place CPT, OPT, H1B, H4EAD, GC, GC-EAD and USC consultants. \n• Placed candidates of different technologies like Java, BA, Devops, Salesforce, Servicenow, Network Engineer, \netc.Automated various infrastructure activities like Continuous Deployment using Ansible playbooks and has \nIntegrated Ansible with <PERSON> on AZURE.", "location": "Itasca, Illinois, United States", "date": {"start": "Dec 2023", "end": "Present"}, "employment_type": "Full-time", "skills": ["Microsoft PowerPoint", "MS Office", "Sourcing"], "company": {"id": "7791426", "name": "Sierra Consulting, Inc.", "url": "https://www.linkedin.com/company/7791426/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C510BAQGV9A2rGOX14Q/company-logo_200_200/company-logo_200_200/0/1631334801931?e=**********&v=beta&t=Uagpd4yGiphPWBvbrxdxuezxy6X4pMB1UPI7UslAUBs", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C510BAQGV9A2rGOX14Q/company-logo_100_100/company-logo_100_100/0/1631334801931?e=**********&v=beta&t=AdgNmrfT4kUEvUfLfjIjyOCLm30Mg3B6Fc0javgI7zM", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C510BAQGV9A2rGOX14Q/company-logo_400_400/company-logo_400_400/0/1631334801932?e=**********&v=beta&t=bY78hZXnIdSfXrvUdMqJxC1ShZGOSvsaiX7P-MCDj7U", "expires_at": **********000}]}}, {"title": "Bench sales recruiter", "description": "• Involved in core marketing bench consultants by posting their resumes on job boards (Dice.com, \nMonster.com, Net Temps, Career Builder, Corp-Corp and Hot Jobs etc.) and through personal network, third \nparty vendors list. \n• Building good rapport with vendors and business partners to maintain healthy business relationship. \n• Liaison between candidates and hiring managers. \n• Building new database through cold calling, from social sites on daily basis, Circulating HOTLIST to them to get \nmore visibility to the consultants available on bench. \n• Interacting with clients, submitting candidates for appropriate requirements and scheduling interviews for the \ncandidates. Achieved quality placements marketing our own W2 consultants \n• Involved in Full life Cycle of Recruitment \n• Keeping the update of the candidates and to know the comfortability and updating/formatting the profile to \nget shortlisted by the vendors.. \n• Coordinating with the consultant in order to know whether they are comfortable with the requirement before \nsubmitting to the Vendor. \n• Negotiate rates With the Vendors/ Clients. \n• Coordinating with the candidates through email/phone to Intimate Consultants about client submission, \ncoordinate interviews, and complete relevant documentation. \n• Follow-up with Vendor to confirm their Date of Joining and keep the respective departments informed of the \nsame. \n• Identifying the potential vendor in the market and maintaining long lasting relationship with them. \n• Identifying the skilled candidate suitable for the requirement from the market through groups, portals, \ndatabase list &amp; social networks. \n• Maintaining Good interpersonal Relation with the Client and the Vendors whom I deal with before.", "location": "United States, Dallas, TX · On-site", "date": {"start": "Apr 2023", "end": "Nov 2023"}, "employment_type": "Full-time", "skills": ["Microsoft Excel", "Microsoft Office", "Negotiation", "Sourcing"], "company": {"id": "13308989", "name": "Sacrosanctinfo LLC", "url": "https://www.linkedin.com/company/13308989/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQHVF8pBjlh-9g/company-logo_200_200/company-logo_200_200/0/1631341038979?e=**********&v=beta&t=cBd_KSewoPlF8lELAEEzRxBghKFjCD2NVoKlvDzx3Ow", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQHVF8pBjlh-9g/company-logo_100_100/company-logo_100_100/0/1631341038979?e=**********&v=beta&t=a5BEtgVyvjVL-JmIk8BMamc2WIAFl_uJYs5HjaiqWbU", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQHVF8pBjlh-9g/company-logo_400_400/company-logo_400_400/0/1631341038979?e=**********&v=beta&t=P9YQG1OFVdRM17uzQMJi8lmgF45_abbNn-pf34lDwtg", "expires_at": **********000}]}}], "skills": [{"skill": "Microsoft PowerPoint"}, {"skill": "MS Office"}, {"skill": "Microsoft Excel"}, {"skill": "Microsoft Office"}, {"skill": "Negotiation"}, {"skill": "Sourcing"}], "certifications": [], "publications": [], "educations": [], "honors": [], "volunteers": []}, {"id": "*********", "urn": "ACoAADfSgvIBOtdI-6nwuVS-6F57SWYLqz4WS08", "public_identifier": "lokesh-rao-a6a790221", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON>", "full_name": "<PERSON><PERSON>", "headline": "Lead Bench Sales Recruiter @ SilverXis Inc. | Full Life Cycle Recruiting", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1632316255928, "created_date": "2021-09-22T13:10:55.928Z", "location": {"country": "United States", "country_code": "US", "city": "Irving, Texas, United States", "postal_code": null}, "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Lead Bench Sales recruiter", "description": "Skills: Sales Management · Sales Operations · Full Life Cycle Recruiting · Interpersonal Skills · Sales Finance · Direct Sales · Sales Performance · Sales Recruitment · Sales Strategy · Sales and Marketing", "location": "Hyderabad, Telangana, India · On-site", "date": {"start": "Nov 2023", "end": "Present"}, "employment_type": "Full-time", "company": {"id": "1744556", "name": "SilverXis Inc.", "url": "https://www.linkedin.com/company/1744556/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQG-LI8QQaxHTA/company-logo_200_200/company-logo_200_200/0/1737978980744/silverxis_inc_logo?e=**********&v=beta&t=k8b_vTdN4Zu-k1sjameKNQ-rDOzJLpC4_vWz7z6WCes", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQG-LI8QQaxHTA/company-logo_100_100/company-logo_100_100/0/1737978980744/silverxis_inc_logo?e=**********&v=beta&t=Icoif_Ybhtgn5EOSXzVfHmN-eu34R5AWVRtxZUMEK7E", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQG-LI8QQaxHTA/company-logo_400_400/company-logo_400_400/0/1737978980744/silverxis_inc_logo?e=**********&v=beta&t=5l33mT0iF5fblh-gd60hk1o9N6poem_PcVW8jMi-nNU", "expires_at": **********000}]}}, {"title": "Senior Sales Executive", "description": "Skills: Sales Management · Sales Operations · Full Life Cycle Recruiting · Interpersonal Skills · Sales Finance · Direct Sales · Sales Performance · Sales Recruitment · Sales Strategy · Sales and Marketing", "date": {"start": "Oct 2019", "end": "Nov 2023"}, "employment_type": "Full-time", "company": {"id": "76397067", "name": "Top Edge Technology Inc.", "url": "https://www.linkedin.com/company/76397067/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQE-OaX74inTJw/company-logo_200_200/company-logo_200_200/0/1630648528608?e=**********&v=beta&t=0uUhVq7uFBVkdkCczNq3YpiFxzJwfAm2ZXMK7oLjY08", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQE-OaX74inTJw/company-logo_100_100/company-logo_100_100/0/1630648528609?e=**********&v=beta&t=8MuhHe91ja1ytb3vr_evRK9OrPopqQAPdm4PJj7Yft4", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQE-OaX74inTJw/company-logo_400_400/company-logo_400_400/0/1630648528609?e=**********&v=beta&t=0q8ucQd0DDMYTgKhl1qBZEvPuHRT0eAqPKRy9V6m-gM", "expires_at": **********000}]}}, {"title": "Bench sales executive", "description": "Skills: Sales Management · Sales Operations · Full Life Cycle Recruiting · Interpersonal Skills · Sales Finance · Direct Sales · Sales Performance · Sales Recruitment · Sales Strategy · Sales and Marketing", "location": "Hyderabad, Telangana, India", "date": {"start": "Feb 2016", "end": "Aug 2019"}, "employment_type": "Full-time", "company": {"name": "Zenith IT Solutions", "url": "https://www.linkedin.com/search/results/all/?keywords=Zenith+IT+Solutions"}}], "skills": [{"skill": "Full Life Cycle Recruiting"}, {"skill": "Interpersonal Skills"}, {"skill": "Sales Finance"}, {"skill": "Sales Operations"}, {"skill": "Sales Performance"}, {"skill": "Direct Sales"}, {"skill": "Sales Strategy"}, {"skill": "Sales and Marketing"}, {"skill": "Sales Management"}, {"skill": "Sales Recruitment"}], "certifications": [], "publications": [], "educations": [], "honors": [], "volunteers": []}, {"id": "1226434256", "urn": "ACoAAEkZ5tAB7ETB1AFXSkmrYICSKnBkhRpqjaM", "public_identifier": "rickin-paul-0688922a2", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON>", "full_name": "<PERSON><PERSON>", "headline": "SR Bench sales recruiter at Aristostek", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1701458620302, "created_date": "2023-12-01T19:23:40.302Z", "location": {"country": "India", "country_code": "IN", "city": "Hyderabad, Telangana, India", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D5603AQGmMOFi_ofq3Q/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/1706215969350?e=**********&v=beta&t=pFzQWHjySJM2tii5IqVO_aO03v2jVEh-pUj8aBa132M", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5603AQGmMOFi_ofq3Q/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1706215969350?e=**********&v=beta&t=k_dJ3HVcHasqbIIDgiPARhtB27g84DMmahw0ftZPcSU", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D5603AQGmMOFi_ofq3Q/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1706215969351?e=**********&v=beta&t=-v1t3gCfVFdK5ycMDbSO0Debx_O1PXDmf7rcS8DlbGQ", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D5603AQGmMOFi_ofq3Q/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1706215969351?e=**********&v=beta&t=eHYntWJvchaa03ZtU8C-8sIEJyS-Zh_969pxo6Ng7IE", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "SR Bench sales recruiter at Aristostek", "date": {"start": "Sep 2023", "end": "Present"}, "company": {"id": "*********", "name": "Aristostek Inc", "url": "https://www.linkedin.com/company/*********/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4E0BAQGxUA1qTe7G9Q/company-logo_200_200/company-logo_200_200/0/1719256742429/aristostek_logo?e=**********&v=beta&t=EhikQJ1GsSFIOaGFO-SiazKLstv1H-O4_rxDSe171rY", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D4E0BAQGxUA1qTe7G9Q/company-logo_100_100/company-logo_100_100/0/1719257087050/aristostek_logo?e=**********&v=beta&t=lUE2FbEe1tFW4bItGp9xFoiLIHYfboJ0BoATT_tv9Uc", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D4E0BAQGxUA1qTe7G9Q/company-logo_400_400/company-logo_400_400/0/1719258042985/aristostek_logo?e=**********&v=beta&t=FQIUZeELLUGUCOP--KxoJgZVaqUmGQyGl-U7PufxPCs", "expires_at": **********000}]}}], "skills": [], "certifications": [], "publications": [], "educations": [{"school": "<PERSON> <PERSON><PERSON><PERSON><PERSON>, Kancheepuram", "date": {"start": "Aug 2019", "end": "Jul 2023"}, "degree": "Bachelor of Engineering - BE, Electrical, Electronics and Communications Engineering"}], "honors": [], "volunteers": []}, {"id": "*********", "urn": "ACoAADeXMmwBYNBQHM5FtVMYX_A5s3ff51lMU0k", "public_identifier": "veeresh-chanda", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Chanda 🇮🇳", "full_name": "<PERSON><PERSON><PERSON> 🇮🇳", "headline": "Seeking Collaboration with Tier-I Vendors | US IT Recruiters & Implementation Partners | US Staffing | Vendor Engagement | DOER", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": 1, "month": 11, "year": null}, "pronoun": null, "created": 1631478030211, "created_date": "2021-09-12T20:20:30.211Z", "location": {"country": "United States", "country_code": "US", "city": "Miami, Florida, United States", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D5603AQGtZzNfTs21DQ/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/1726694141063?e=**********&v=beta&t=6p5T4QFznNyXkUkEPyMjaxoiYTTVTu6hNsrwTo2YAqg", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5603AQGtZzNfTs21DQ/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1726694141063?e=**********&v=beta&t=31PQD-WpqGWDfct-YpKk-fpjgquk77OygPGyNixThE0", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D5603AQGtZzNfTs21DQ/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1726694141063?e=**********&v=beta&t=jpP1aTcmKoQWtzeF9mHoIBo4WULLgCgmvHDpzO1qw0k", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D5603AQGtZzNfTs21DQ/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1726694141085?e=**********&v=beta&t=cTOjzAmv2rG0-aESY8szorPWZcr5qjTgbDob0HlyYZ4", "expires_at": **********000}], "cover": [{"width": 800, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5616AQHvHsB85WUcpA/profile-displaybackgroundimage-shrink_200_800/profile-displaybackgroundimage-shrink_200_800/0/1************?e=**********&v=beta&t=kl_F5jdRNNB7jdmbN4XlNHxK-7ZwAS0Ud7IJsdXH8CM", "expires_at": **********000}, {"width": 1400, "height": 350, "url": "https://media.licdn.com/dms/image/v2/D5616AQHvHsB85WUcpA/profile-displaybackgroundimage-shrink_350_1400/profile-displaybackgroundimage-shrink_350_1400/0/1************?e=**********&v=beta&t=gQ5jx0TZWSAzhNBMIlkOzwSbdpstIWCuPwrPCvlLoaQ", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Bench Sales Recruiter", "description": "Skills: 1099 · Communication · Contract Recruitment · Crop to Crop · H1B · Marketing · Optional Practical Training · STEM OPT · US Time Zones · US Tax Terms", "location": "Hyderabad, Telangana, India · On-site", "date": {"start": "Aug 2024", "end": "Jan 2025"}, "employment_type": "Full-time", "company": {"id": "33458572", "name": "Apptrics LLC", "url": "https://www.linkedin.com/company/33458572/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFiHekexCBNfg/company-logo_200_200/company-logo_200_200/0/1630531047065/apptrics_logo?e=**********&v=beta&t=xbAOdEuI3m43Y2_DzRGRd2qFC1V2ovCAfBNFUyisDeA", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFiHekexCBNfg/company-logo_100_100/company-logo_100_100/0/1630531047065/apptrics_logo?e=**********&v=beta&t=CRuVqk9gQnJbfBTrtsrM0wYyRsXPhv2BhdYJsqZptIc", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFiHekexCBNfg/company-logo_400_400/company-logo_400_400/0/1630531047065/apptrics_logo?e=**********&v=beta&t=BG5rwILtDooSjiVW9dPJpNcl_oPOBRpvlOTROLtj5Ls", "expires_at": **********000}]}, "media": [{"description": null, "thumbnail": [{"width": 909, "height": 1286, "url": "https://media.licdn.com/dms/image/v2/D4D2DAQEW1us4I5QF0A/profile-treasury-document-cover-images_1280/profile-treasury-document-cover-images_1280/0/1738225546298?e=**********&v=beta&t=sD28Tg6wljq378N_Ftf0s9b_OFvXp0VYD4DFshbo8T0", "expires_at": **********000}, {"width": 570, "height": 806, "url": "https://media.licdn.com/dms/image/v2/D4D2DAQEW1us4I5QF0A/profile-treasury-document-cover-images_800/profile-treasury-document-cover-images_800/0/1738225546455?e=**********&v=beta&t=wqZgeGsltaWXUSIQ89wQHSjd-9TCwUqTe4t2sre32Cw", "expires_at": **********000}, {"width": 1364, "height": 1929, "url": "https://media.licdn.com/dms/image/v2/D4D2DAQEW1us4I5QF0A/profile-treasury-document-cover-images_1920/profile-treasury-document-cover-images_1920/0/1738225549363?e=**********&v=beta&t=HRQA0twhO5_i-Bj0TXSzeCYOUemnnDJWL4jb-ywCOQw", "expires_at": **********000}, {"width": 347, "height": 491, "url": "https://media.licdn.com/dms/image/v2/D4D2DAQEW1us4I5QF0A/profile-treasury-document-cover-images_480/profile-treasury-document-cover-images_480/0/1738225545052?e=**********&v=beta&t=TUqxjJGfrtIAglw1-zylraiuzRakSZ3DDqGQZVFs30I", "expires_at": **********000}], "title": "<PERSON><PERSON><PERSON> offer letter.pdf"}]}, {"title": "Layoff/position eliminated", "description": "I was laid off from the company and during this crucial time I started to brush up my skills", "location": "Nandyal, Andhra Pradesh", "date": {"start": "Mar 2023", "end": "May 2023"}, "company": {"name": "Career Break", "url": null}}, {"title": "Java Software Developer", "description": "Skills: Freemarker · Java Development · Spring Boot · Communication · Advanced Java · PostgreSQL", "location": "Nandyal Mandal, Andhra Pradesh, India", "date": {"start": "Aug 2022", "end": "Feb 2023"}, "employment_type": "Full-time", "company": {"id": "4815482", "name": "Thrymr Software", "url": "https://www.linkedin.com/company/4815482/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C560BAQHDlhOUI105sQ/company-logo_200_200/company-logo_200_200/0/1631342715301?e=**********&v=beta&t=sU3vZDgOVeLYJLy8pI0NkyabMsWd5LbayNWoKW1ue_E", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C560BAQHDlhOUI105sQ/company-logo_100_100/company-logo_100_100/0/1631342715301?e=**********&v=beta&t=U1663Lq6du3fVRWhgMM4F_SrmbiRdqvjrxzb0w20qbg", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C560BAQHDlhOUI105sQ/company-logo_400_400/company-logo_400_400/0/1631342715301?e=**********&v=beta&t=TjUFvFiLtleUVjYFB7JJr0omkPF8y3Nl9yLLgSxsiX0", "expires_at": **********000}]}, "media": [{"description": null, "thumbnail": [{"width": 876, "height": 1287, "url": "https://media.licdn.com/dms/image/v2/D562DAQGbGpLS5oeTgQ/profile-treasury-document-cover-images_1280/profile-treasury-document-cover-images_1280/0/1711012123566?e=**********&v=beta&t=h18fxoHlIN_Rx_Z0x-ppQhYCNFZF5iNR6PuQOv-cfKI", "expires_at": **********000}, {"width": 546, "height": 802, "url": "https://media.licdn.com/dms/image/v2/D562DAQGbGpLS5oeTgQ/profile-treasury-document-cover-images_800/profile-treasury-document-cover-images_800/0/1711012122939?e=**********&v=beta&t=a0twH86VWKSR9gEgVOcHJi8Tc7snofoDFMkgh8MCJyc", "expires_at": **********000}, {"width": 1310, "height": 1925, "url": "https://media.licdn.com/dms/image/v2/D562DAQGbGpLS5oeTgQ/profile-treasury-document-cover-images_1920/profile-treasury-document-cover-images_1920/0/1711012123784?e=**********&v=beta&t=Hkd8upWP5WRdxHSLfyf7lSwUSi7gHiY93inDN1I9PgU", "expires_at": **********000}, {"width": 329, "height": 484, "url": "https://media.licdn.com/dms/image/v2/D562DAQGbGpLS5oeTgQ/profile-treasury-document-cover-images_480/profile-treasury-document-cover-images_480/0/1711012122730?e=**********&v=beta&t=OuYdpBPJZgUagsTqavTE9e6h9tPi_cbgUHtP6si7DHE", "expires_at": **********000}], "title": "veeresh offer letter.pdf"}]}, {"title": "Java Developer Intern", "description": "Skills: Learning · Java · Back-End Web Development · Spring Boot · Ubuntu · SQL · Spring MVC · Core Java · PostgreSQL · Object-Oriented Programming (OOP)", "location": "Nandyal, Andhra Pradesh, India", "date": {"start": "Feb 2022", "end": "Jul 2022"}, "employment_type": "Internship", "company": {"id": "4815482", "name": "Thrymr Software", "url": "https://www.linkedin.com/company/4815482/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C560BAQHDlhOUI105sQ/company-logo_200_200/company-logo_200_200/0/1631342715301?e=**********&v=beta&t=sU3vZDgOVeLYJLy8pI0NkyabMsWd5LbayNWoKW1ue_E", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C560BAQHDlhOUI105sQ/company-logo_100_100/company-logo_100_100/0/1631342715301?e=**********&v=beta&t=U1663Lq6du3fVRWhgMM4F_SrmbiRdqvjrxzb0w20qbg", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C560BAQHDlhOUI105sQ/company-logo_400_400/company-logo_400_400/0/1631342715301?e=**********&v=beta&t=TjUFvFiLtleUVjYFB7JJr0omkPF8y3Nl9yLLgSxsiX0", "expires_at": **********000}]}, "media": [{"description": null, "thumbnail": [{"width": 909, "height": 1286, "url": "https://media.licdn.com/dms/image/v2/D562DAQHEEZjihRHPgw/profile-treasury-document-cover-images_1280/profile-treasury-document-cover-images_1280/0/1711012019561?e=**********&v=beta&t=QyTrY67ArSWO7T34qeCd6blPVth6wwyfmej85drbH4c", "expires_at": **********000}, {"width": 570, "height": 806, "url": "https://media.licdn.com/dms/image/v2/D562DAQHEEZjihRHPgw/profile-treasury-document-cover-images_800/profile-treasury-document-cover-images_800/0/1711012019367?e=**********&v=beta&t=NeL_pJi3HxhwocJooyiCIdbXPo-IR8F1yS8UjY6DIlw", "expires_at": **********000}, {"width": 1363, "height": 1929, "url": "https://media.licdn.com/dms/image/v2/D562DAQHEEZjihRHPgw/profile-treasury-document-cover-images_1920/profile-treasury-document-cover-images_1920/0/1711012019448?e=**********&v=beta&t=Sq1geoq-VDljmIg0cwRIBnYiEJBW578hYyEhwoHD_zM", "expires_at": **********000}, {"width": 347, "height": 491, "url": "https://media.licdn.com/dms/image/v2/D562DAQHEEZjihRHPgw/profile-treasury-document-cover-images_480/profile-treasury-document-cover-images_480/0/1711012019220?e=**********&v=beta&t=hXCh1itpCLqoOZGq8Zih3PPunUx182JT4ZsMYaf_e_s", "expires_at": **********000}], "title": "Software developer Internship certificate.pdf"}]}], "skills": [{"skill": "Contract Recruitment"}, {"skill": "Optional Practical Training"}, {"skill": "Curricular Practical Training"}, {"skill": "STEM OPT"}, {"skill": "US Tax Terms"}, {"skill": "Marketing"}, {"skill": "Communication Skills"}, {"skill": "Management"}, {"skill": "US Time Zones"}, {"skill": "Third-Party Vendor Management"}, {"skill": "React.js"}, {"skill": "Bootstrap (Framework)"}, {"skill": "JavaScript"}, {"skill": "Cascading Style Sheets (CSS)"}, {"skill": "HTML"}, {"skill": "W2"}, {"skill": "Crop to Crop"}, {"skill": "One man crop"}, {"skill": "1099"}, {"skill": "H1B"}], "certifications": [{"title": "Bench Sales Recruiter Program Certification", "authority": "Talent Hub Solutions", "credential_url": null, "issued_at": "Sep 2024"}], "publications": [], "educations": [{"school": "Sri Rama<PERSON><PERSON>hna Degree College (Autonomous)", "date": {"start": "Jun 2018", "end": "Nov 2021"}, "degree": "Bachelor of Science - BS, Science", "grade": "73.15", "media": [{"description": null, "thumbnail": [{"width": 909, "height": 1285, "url": "https://media.licdn.com/dms/image/v2/D562DAQFsLKhKpuxJcw/profile-treasury-document-cover-images_1280/profile-treasury-document-cover-images_1280/0/1711012337904?e=**********&v=beta&t=AhSPzQ1hp-X2X0qddLLMcrv3k_XYWzfJi2zel1yT7A8", "expires_at": **********000}, {"width": 570, "height": 806, "url": "https://media.licdn.com/dms/image/v2/D562DAQFsLKhKpuxJcw/profile-treasury-document-cover-images_800/profile-treasury-document-cover-images_800/0/1711012337778?e=**********&v=beta&t=ngwcAMe6QQcHfy9Hm-02YSuVuf-bBCZkRc9PhPieFbc", "expires_at": **********000}, {"width": 1364, "height": 1928, "url": "https://media.licdn.com/dms/image/v2/D562DAQFsLKhKpuxJcw/profile-treasury-document-cover-images_1920/profile-treasury-document-cover-images_1920/0/1711012338356?e=**********&v=beta&t=KMsiWkdv1YrLFvw8bZXxtLXi3t92ivDPnPYKAAX1ZNE", "expires_at": **********000}, {"width": 347, "height": 490, "url": "https://media.licdn.com/dms/image/v2/D562DAQFsLKhKpuxJcw/profile-treasury-document-cover-images_480/profile-treasury-document-cover-images_480/0/1711012337679?e=**********&v=beta&t=_wYvkoMMbL4585hw5AeEX0yb8LUxOuP4LBljUkd3yYE", "expires_at": **********000}], "title": "Degree Marks memo.pdf"}]}, {"school": "Narayana Medical Academy", "date": {"start": "Jun 2017", "end": "May 2018"}, "degree": "Long term NEET Coaching", "media": [{"description": null, "thumbnail": [{"width": 935, "height": 1286, "url": "https://media.licdn.com/dms/image/v2/D562DAQEJRPYBTxYk9w/profile-treasury-document-cover-images_1280/profile-treasury-document-cover-images_1280/0/1711012515355?e=**********&v=beta&t=wy3aUJORY8-Art1vt-FFnBbF5elQknaWqU-Y5ho5ZGo", "expires_at": **********000}, {"width": 586, "height": 807, "url": "https://media.licdn.com/dms/image/v2/D562DAQEJRPYBTxYk9w/profile-treasury-document-cover-images_800/profile-treasury-document-cover-images_800/0/1711012515359?e=**********&v=beta&t=e-IWahFgjXmVqDmfQB-ePzSoIlZLC9F_qs7w8TXTKEY", "expires_at": **********000}, {"width": 1402, "height": 1929, "url": "https://media.licdn.com/dms/image/v2/D562DAQEJRPYBTxYk9w/profile-treasury-document-cover-images_1920/profile-treasury-document-cover-images_1920/0/1711012515341?e=**********&v=beta&t=sy7u3f-ZBs5NhnZF5w-MAB-xW15xS_7U46-3HMtNq6U", "expires_at": **********000}, {"width": 357, "height": 491, "url": "https://media.licdn.com/dms/image/v2/D562DAQEJRPYBTxYk9w/profile-treasury-document-cover-images_480/profile-treasury-document-cover-images_480/0/1711012515286?e=**********&v=beta&t=9yS0b1K3JAotNegHTXwHYk7HqxkivtuRuHAiqsEYBP0", "expires_at": **********000}], "title": "Gap certificate.pdf"}]}, {"school": "Sri Chaitanya Junior College", "date": {"start": "Jun 2015", "end": "Mar 2017"}, "degree": "Bi.p.c, Biology/Biological Sciences, General", "grade": "93.8", "media": [{"description": null, "thumbnail": [{"width": 935, "height": 1286, "url": "https://media.licdn.com/dms/image/v2/D562DAQEjVcc32IpoyQ/profile-treasury-document-cover-images_1280/profile-treasury-document-cover-images_1280/0/1711012437462?e=**********&v=beta&t=8jCrNSK4_opP4TxYrFqStEm_UsmdXeg6Pmlnk5Xhbvw", "expires_at": **********000}, {"width": 586, "height": 807, "url": "https://media.licdn.com/dms/image/v2/D562DAQEjVcc32IpoyQ/profile-treasury-document-cover-images_800/profile-treasury-document-cover-images_800/0/1711012438157?e=**********&v=beta&t=_D6OGydfVwQWLE9APg6zdbwdmhGuKNnmDkN0K0PqhbI", "expires_at": **********000}, {"width": 1402, "height": 1929, "url": "https://media.licdn.com/dms/image/v2/D562DAQEjVcc32IpoyQ/profile-treasury-document-cover-images_1920/profile-treasury-document-cover-images_1920/0/1711012437667?e=**********&v=beta&t=WVRXo1yExSfolQSIElkdyo2vTaOgHXuRS7q-tIHaQgY", "expires_at": **********000}, {"width": 357, "height": 491, "url": "https://media.licdn.com/dms/image/v2/D562DAQEjVcc32IpoyQ/profile-treasury-document-cover-images_480/profile-treasury-document-cover-images_480/0/1711012437547?e=**********&v=beta&t=KYyY-HJZ7j8O9vtV0MtoXkeISuDaB5xoKUmj_uXq2ao", "expires_at": **********000}], "title": "Intermediate Marks memo.pdf"}]}, {"school": "Vijayanikethan EM High School ", "date": {"start": "Jun 2014", "end": "May 2015"}, "grade": "90.25", "media": [{"description": null, "thumbnail": [{"width": 994, "height": 1287, "url": "https://media.licdn.com/dms/image/v2/D562DAQFbjjFHRk0wXg/profile-treasury-document-cover-images_1280/profile-treasury-document-cover-images_1280/0/1711012477758?e=**********&v=beta&t=lLNS5SbJdwlaXxeeYUa_2TZ943NmD0OyuSzMISY_ANQ", "expires_at": **********000}, {"width": 620, "height": 802, "url": "https://media.licdn.com/dms/image/v2/D562DAQFbjjFHRk0wXg/profile-treasury-document-cover-images_800/profile-treasury-document-cover-images_800/0/1711012477479?e=**********&v=beta&t=PjmVVEoZILcdE5L3knqJDIwRnCZ8KFFJlWc6qQLy5sw", "expires_at": **********000}, {"width": 1487, "height": 1925, "url": "https://media.licdn.com/dms/image/v2/D562DAQFbjjFHRk0wXg/profile-treasury-document-cover-images_1920/profile-treasury-document-cover-images_1920/0/1711012477939?e=**********&v=beta&t=4Cy2zFTWT_pqI57oxacfIW0kssOnEyM_5I9-aHeFWTM", "expires_at": **********000}, {"width": 374, "height": 484, "url": "https://media.licdn.com/dms/image/v2/D562DAQFbjjFHRk0wXg/profile-treasury-document-cover-images_480/profile-treasury-document-cover-images_480/0/1711012477513?e=**********&v=beta&t=rPKVYUdhEoR5-jUI7FZCn412cJh3Wr4Nxup8ZI51J2E", "expires_at": **********000}], "title": "Tenth Marks memo.pdf"}]}], "honors": [], "volunteers": []}, {"id": "861414574", "urn": "ACoAADNYJK4BMUPY8S-XtUZ5FUnRI3i05-eCC1w", "public_identifier": "shalni-sukka-97a59b200", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON>", "full_name": "<PERSON><PERSON><PERSON>", "headline": "Looking for new opportunities", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": 18, "month": 1, "year": null}, "pronoun": null, "created": 1606218003092, "created_date": "2020-11-24T11:40:03.092Z", "location": {"country": "United States", "country_code": "US", "city": "Lexington, South Carolina, United States", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D5603AQGOWR0h-gqykw/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/1684817118844?e=**********&v=beta&t=5WS1GRMpK9M29SdcF3P1q_XGNt7VOd8mCJlhCYrAPYo", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5603AQGOWR0h-gqykw/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1684817118844?e=**********&v=beta&t=w2P7h3bX4fIVuSNdFNVYyC2SDQiOjIrRgkugLx1mnEg", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D5603AQGOWR0h-gqykw/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1684817118844?e=**********&v=beta&t=kxz1D13-FUmgkCTc4fsYtz85oq_pnr3Jcc4tA_C2tq0", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D5603AQGOWR0h-gqykw/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1684817118844?e=**********&v=beta&t=VdNqjw6GV0QxpB-ptr8VpTfrTODfgVuqA3h8pvl32sY", "expires_at": **********000}], "cover": [{"width": 744, "height": 186, "url": "https://media.licdn.com/dms/image/v2/D5616AQG3lNz5bW8PUA/profile-displaybackgroundimage-shrink_200_800/profile-displaybackgroundimage-shrink_200_800/0/1711487948744?e=**********&v=beta&t=No4Bab_1FoOnB6RU2FmA-qu9n6YtlCcEg861iZSeO4Q", "expires_at": **********000}, {"width": 744, "height": 186, "url": "https://media.licdn.com/dms/image/v2/D5616AQG3lNz5bW8PUA/profile-displaybackgroundimage-shrink_350_1400/profile-displaybackgroundimage-shrink_350_1400/0/1711487948744?e=**********&v=beta&t=FvA2kpmJ0oh8EmhFINE_W4WQUIqmByhm1I7dK1S27u8", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Human Resources Manager", "description": "Identified staffing needs, conducted interviews, and selected qualified candidates for appropriate roles. Prepared and managed invoices while maintaining accurate timesheets for consultants. Proactively followed up on pending invoices with vendors to ensure timely payments. Oversaw post-placement processes, including Statement of Work (SOW), Purchase Order (PO) and Master Service Agreement (MSA) management. Conducted screening calls for intern consultants, guiding them to enhance their interview performance.", "date": {"start": "Sep 2024", "end": "May 2025"}, "skills": ["Human Capital Management", "Payroll Services", "Hiring", "Recruiting", "Marketing", "Corporate Recruiting", "Advertising", "Global Human Resources Management"], "company": {"id": "90832153", "name": "Xenonsoft Tech", "url": "https://www.linkedin.com/company/90832153/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFTS6EL83Xr6Q/company-logo_200_200/company-logo_200_200/0/1675196492716?e=**********&v=beta&t=jc7jopyBTtjfaDMfOzf_a8nOf6ow9GrDWXIerjWa2Oo", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFTS6EL83Xr6Q/company-logo_100_100/company-logo_100_100/0/1675196492716?e=**********&v=beta&t=dRRzBqqIXk-7gHiR9A6CkG7PxG8EcInLtMyKwBj5ZkE", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFTS6EL83Xr6Q/company-logo_400_400/company-logo_400_400/0/1675196492716?e=**********&v=beta&t=_FRMz9Jmw11jFIW-FYXfm398O94KLcXyBxvHeUJ0afE", "expires_at": **********000}]}}, {"title": "Talent Acquisition Specialist", "description": "•\tResponsible for placing the Consultants who are on bench for the Prime Vendor, Implementations and Direct Client requirements.\n•\tUnderstanding the opening, technical interviews, negotiations, closing the deal and maintaining the relationship with clients and consultants.\n•\tInvolved in posting the resumes in various job sites and sourcing the right jobs.\n•\tMaking Follow ups with different vendors for previously submitted positions.\n•\tSourcing, identifying, the Requirements from Top vendors to place Consultants in quick turn.", "date": {"start": "May 2024", "end": "Feb 2025"}, "company": {"id": "90832153", "name": "Xenonsoft Tech", "url": "https://www.linkedin.com/company/90832153/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFTS6EL83Xr6Q/company-logo_200_200/company-logo_200_200/0/1675196492716?e=**********&v=beta&t=jc7jopyBTtjfaDMfOzf_a8nOf6ow9GrDWXIerjWa2Oo", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFTS6EL83Xr6Q/company-logo_100_100/company-logo_100_100/0/1675196492716?e=**********&v=beta&t=dRRzBqqIXk-7gHiR9A6CkG7PxG8EcInLtMyKwBj5ZkE", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFTS6EL83Xr6Q/company-logo_400_400/company-logo_400_400/0/1675196492716?e=**********&v=beta&t=_FRMz9Jmw11jFIW-FYXfm398O94KLcXyBxvHeUJ0afE", "expires_at": **********000}]}}, {"title": "Benchsales", "description": "•\tInvolved into full-cycle recruiting: interviewing, negotiation and closing candidates for assigned requisitions.\n•\tUse Job boards like - Dice, Monsters, CareerBuilder, Indeed and LinkedIn.\n•\tUsing different sourcing techniques like- internet souring, networking, job portals, Boolean Search, Social Media Sourcing\n•\tExtensive experience working with candidates for Full Time position\n•\tSalary negotiation.\n•\tExpertise in Sourcing IT Professionals from Junior Position till the senior level\n•\tTelephonic screening to asses weathers the candidate qualify for open requisitions.\n•\tScheduling technical interviews.", "location": "Huston, Texas United states  · On-site", "date": {"start": "Feb 2023", "end": "May 2024"}, "employment_type": "Full-time", "company": {"id": "85099079", "name": "Blueorigin ITStaffing", "url": "https://www.linkedin.com/company/85099079/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQEock5jnEJg0w/company-logo_200_200/company-logo_200_200/0/1650294000854?e=**********&v=beta&t=sJr8aVs29D1M0YmmKUPnhytkXcTuvr1A8BzTrPdIl7w", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQEock5jnEJg0w/company-logo_100_100/company-logo_100_100/0/1650294000854?e=**********&v=beta&t=kfZXDOueC1aQ4EIl8OoKzjOdW_qq-PWRdFXcwLb4VhA", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQEock5jnEJg0w/company-logo_400_400/company-logo_400_400/0/1650294000854?e=**********&v=beta&t=jvGtyzqx0mBXBTSbSK72fo2f56-7zblOepl5_S2qm7I", "expires_at": **********000}]}}], "skills": [{"skill": "Human Capital Management", "num_endorsements": 1}, {"skill": "Payroll Processing", "num_endorsements": 1}, {"skill": "Payroll Taxes", "num_endorsements": 1}, {"skill": "Payroll Services"}, {"skill": "Payroll Management"}, {"skill": "Hiring"}, {"skill": "Recruiting"}, {"skill": "Marketing"}, {"skill": "Corporate Recruiting"}, {"skill": "Advertising"}, {"skill": "Global Human Resources Management"}], "certifications": [], "publications": [], "educations": [{"school": "Osmania University, Hyderabad", "date": {}, "degree": "Bachelor of Commerce - BCom, Computer Software and Media Applications"}], "honors": [], "volunteers": []}, {"id": "*********", "urn": "ACoAADrOWv8Bpb9Jkw3P7wjkrohVGO2Sq5wapN8", "public_identifier": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-", "first_name": "<PERSON><PERSON><PERSON><PERSON>", "last_name": "G", "full_name": "Kowshik G", "headline": "Senior React Native Developer  || React Native || Actively looking for C2C position.", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1648846821380, "created_date": "2022-04-01T21:00:21.380Z", "location": {"country_code": "US", "city": "United States", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D5603AQGzElQKF7ZKSQ/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/1708716070266?e=**********&v=beta&t=suuomOWVga4InStKGlOMT3gB7kFmg_c7yNMgs67Aqow", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5603AQGzElQKF7ZKSQ/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1708716070266?e=**********&v=beta&t=3UP1MNiTgDx-SxNXmmkbS3r20JRrrezF-OZz_Ytu6zI", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D5603AQGzElQKF7ZKSQ/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1708716070266?e=**********&v=beta&t=R4m03H02G8vb22GQuI9UnqJ4-lTzc3nltA0lGzm8QSY", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D5603AQGzElQKF7ZKSQ/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1708716070266?e=**********&v=beta&t=FWtSkPGfD9WRy2qdaR1LNkQX24ir0wHe1gdavHq2Ieg", "expires_at": **********000}], "cover": [{"width": 800, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E16AQHQDuNRIvJFlw/profile-displaybackgroundimage-shrink_200_800/profile-displaybackgroundimage-shrink_200_800/0/1648847562982?e=**********&v=beta&t=_IImouDpNVrGiO3PYvP7ZNYvohsLgyV8dcMEup9DR6I", "expires_at": **********000}, {"width": 1400, "height": 350, "url": "https://media.licdn.com/dms/image/v2/C4E16AQHQDuNRIvJFlw/profile-displaybackgroundimage-shrink_350_1400/profile-displaybackgroundimage-shrink_350_1400/0/1648847562982?e=**********&v=beta&t=2bRNNkTIDlv-mlMQHobneQMH0X4tWBdfzYFTBNdhmLM", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Lead React Native Developer", "description": "Skills: Node.js · mongoDB · Express.js · Kubernetes · Bootstrap (Framework) · Azure Kubernetes Service (AKS) · GraphQL · Redux.js · Web API · HTML · Cascading Style Sheets (CSS) · Material-UI · React Native", "location": "California, United States · Remote", "date": {"start": "Jan 2022", "end": "Present"}, "employment_type": "Contract", "company": {"id": "492307", "name": "Silicon Valley Bank", "url": "https://www.linkedin.com/company/492307/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C560BAQGrJpbnJBKLIQ/company-logo_200_200/company-logo_200_200/0/*************/silicon_valley_bank_logo?e=**********&v=beta&t=LGbZh3UVq_Q6M8_OGSv2uyemILQlH_Qc27Jhnkq6p88", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C560BAQGrJpbnJBKLIQ/company-logo_100_100/company-logo_100_100/0/*************/silicon_valley_bank_logo?e=**********&v=beta&t=ldqMs2p3MvRE1IGvFxumlzGwgtohCXESuNf_sZGBiZs", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C560BAQGrJpbnJBKLIQ/company-logo_400_400/company-logo_400_400/0/*************/silicon_valley_bank_logo?e=**********&v=beta&t=ivKoEXDQ2La28HAhIa2bFcLm97AKkPH4ldVVZRjCnos", "expires_at": **********000}]}}, {"title": "Senior React Native Developer", "location": "Houston, Texas, United States", "date": {"start": "May 2019", "end": "Dec 2021"}, "employment_type": "Contract", "company": {"id": "1271", "name": "Shell", "url": "https://www.linkedin.com/company/1271/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGN30g7aSl4NA/company-logo_200_200/company-logo_200_200/0/*************?e=**********&v=beta&t=G5LFL_AwYVrWvUaOIstURfjKb2fJSdv_lH-QmOgkxlk", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGN30g7aSl4NA/company-logo_100_100/company-logo_100_100/0/*************?e=**********&v=beta&t=F-Mrwm4Q0Nth5HMx5tp-jo03P1g838ZA7i-4CV16uRI", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGN30g7aSl4NA/company-logo_400_400/company-logo_400_400/0/*************?e=**********&v=beta&t=VTHyOY5yGeI73c2Do1WaLuMVCKRyz_wm0l9_q2EXi4Q", "expires_at": **********000}]}}, {"title": "Application Developer", "location": "Hyderabad, Telangana, India", "date": {"start": "Jan 2017", "end": "Apr 2019"}, "company": {"id": "390309", "name": "ITS", "url": "https://www.linkedin.com/company/390309/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGA-fvN-0Z1Aw/company-logo_200_200/company-logo_200_200/0/1675780024674/its_infocom_logo?e=**********&v=beta&t=XQYkEHr9gykaBzDIhaU6e_axq7K6pAphstBI9FtrtH8", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGA-fvN-0Z1Aw/company-logo_100_100/company-logo_100_100/0/1675780024674/its_infocom_logo?e=**********&v=beta&t=T9CuZnXEBoluhw43HrQ3vme76r1bh50EERP2HAsxbd4", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGA-fvN-0Z1Aw/company-logo_400_400/company-logo_400_400/0/1675780024674/its_infocom_logo?e=**********&v=beta&t=JisisOlJIkKmkcbvYaFWMZYrtcipmRlc2fKD8Kc9Bv4", "expires_at": **********000}]}}, {"title": "Software Developer", "location": "Hyderabad, Telangana, India", "date": {"start": "Jan 2015", "end": "Dec 2016"}, "company": {"id": "164380", "name": "Innovative Solutions", "url": "https://www.linkedin.com/company/164380/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFzUD75vsGeIg/company-logo_200_200/company-logo_200_200/0/1630520207602/innovative_solutions_logo?e=**********&v=beta&t=VWeHEu3HKvEWsKB0fGbkVSnHnaQ7SdX8QRcquZgJQA4", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFzUD75vsGeIg/company-logo_100_100/company-logo_100_100/0/1630520207603/innovative_solutions_logo?e=**********&v=beta&t=oui98H-8LGKBJn40ALMIscG-4MDTkyahMy8Mg3HTBf8", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFzUD75vsGeIg/company-logo_400_400/company-logo_400_400/0/1630520207603/innovative_solutions_logo?e=**********&v=beta&t=kbbhPksC7tXaitGzs5oaIzXoL6_TsRTJGC4oQm6xeFw", "expires_at": **********000}]}}], "skills": [{"skill": "React Native"}, {"skill": "Material-UI"}, {"skill": "Express.js"}, {"skill": "Kubernetes"}, {"skill": "Bootstrap (Framework)"}, {"skill": "Azure Kubernetes Service (AKS)"}, {"skill": "GraphQL"}, {"skill": "Redux.js"}, {"skill": "Web API"}, {"skill": "HTML"}, {"skill": "Cascading Style Sheets (CSS)"}, {"skill": "React.js"}, {"skill": "JavaScript"}, {"skill": "TypeScript"}, {"skill": "AJAX"}, {"skill": "Node.js"}, {"skill": "mongoDB"}, {"skill": "PostgreSQL"}], "certifications": [], "publications": [], "educations": [], "honors": [], "volunteers": []}, {"id": "*********", "urn": "ACoAACOGw84BsHH8x2WkqZYkk5YnCBCWq2HWh80", "public_identifier": "raghuma-reddy-k-46673b147", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Reddy.K", "full_name": "Raghuma Reddy.K", "headline": "Lead Bench Sales/IT Technical Recruiter at Archon Software LLC", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1502289753015, "created_date": "2017-08-09T14:42:33.015Z", "location": {"country": "India", "country_code": "IN", "city": "Hyderabad, Telangana, India", "postal_code": null}, "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Lead Bench Sales Recruiter", "location": "On-site", "date": {"start": "Apr 2024", "end": "Present"}, "employment_type": "Full-time", "company": {"id": "99000530", "name": "Archon Software LLC", "url": "https://www.linkedin.com/company/99000530/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQErJX3HJPM_qA/company-logo_200_200/company-logo_200_200/0/1700354965778?e=**********&v=beta&t=sHX9mLoJWnPLSr2tiF3H_kcov7rwzXmwqZXVmyGj95Q", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQErJX3HJPM_qA/company-logo_100_100/company-logo_100_100/0/1700354965778?e=**********&v=beta&t=sa7hsHcUsrPNWd033piKuiQGsxknr9oqLfYuQeavxNo", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQErJX3HJPM_qA/company-logo_400_400/company-logo_400_400/0/1700354965778?e=**********&v=beta&t=4cU8Eyab08fgl7zv-wj6JP7FHe6KPKlkvlkqJiIw6ps", "expires_at": **********000}]}}, {"title": "Sr.Bench Sales/Technical Recruiter", "description": "Recruit IT professionals in various technologies.\n\n• Conduct Internet search using Sourcing Tools like: Head Hunting, References, Job Portals (like\n\n<PERSON>, Di<PERSON>).\n\n• Discussing with the client about the description and responsibilities for a job and the level of\n\nperformance expectation from the candidate (expert/intermediate/Senior/Mid/Junior, etc.)\n\n• Bill Rate Negotiation\n\n• Conduct preliminary phone interviews and perform reference checks.\n\n• Develop and format resumes for effective initial presentation of the candidate to the clients\n\n• Finding and hiring candidates on the company’s payroll, W2 on an hourly or on a yearly basis.\n\n• Schedule in person / Telephonic (onsite / phone) interviews between candidate and client.\n\n• Building up good Database of candidates and clients.\n\n• Maintain accurate applicant tracking files for all the candidates considered for a specific position.\n\nGeneral administrative responsibilities as needed.\n\n• Understanding the clients requirements, coordinating for short listing and screening including\n\npreliminary interview of the candidates.\n\n• Finding suitable requirements for candidates on bench.\n\n• Extensive search on Internet sites for job postings.\n\n• Involved in active submissions of candidates our H1 C2C resources.\n\n• Placed Green card holders and Citizens for different skills.\n\n• Enhancing company’s database to be used by all recruiters for sourcing purposes.\n\n• Monitoring and follow-up till final selection.\n\n• Responsible to understand and analyze the requirements in different domain categories.\n\nIT Recruiter/ Sales BDE", "location": "Georgia", "date": {"start": "Jun 2017", "end": "Apr 2024"}, "company": {"name": "Spry Info Solutions,INC.", "url": "https://www.linkedin.com/search/results/all/?keywords=Spry+Info+Solutions%2CINC%2E"}}], "skills": [], "certifications": [], "publications": [], "educations": [{"school": "Osmania University", "date": {"start": "2007", "end": "2010"}, "degree": "Bachelor of Commerce - BCom"}], "honors": [], "volunteers": []}, {"id": "1083219009", "urn": "ACoAAECQnEEBCLlXjzKeIxUBtJ9UCd7iTKBDKAE", "public_identifier": "naveen-beesa-a29926262", "first_name": "Na<PERSON><PERSON>", "last_name": "Beesa", "full_name": "<PERSON><PERSON><PERSON>", "headline": "\"Empowering Companies with the Right Talent, Right Now | Sr.Sales Recruiter”.\n", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": 24, "month": 7, "year": null}, "pronoun": null, "created": 1674074573275, "created_date": "2023-01-18T20:42:53.275Z", "location": {"country": "India", "country_code": "IN", "city": "Hyderabad, Telangana, India", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D5603AQF_WXTe2TucrA/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/1728453592815?e=**********&v=beta&t=6mmjdhLxRCr11VWpcT063C0DWZE5L_mOdf0zr8cSIwY", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5603AQF_WXTe2TucrA/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1728453592815?e=**********&v=beta&t=i2DHCDpUaFdgxDE0xYTxBTLYRLh6aecJZjO_GYXZE-4", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D5603AQF_WXTe2TucrA/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1728453592815?e=**********&v=beta&t=jwhrOHiR6OPsBf_z5TEFBSVNFZZl4cFk__-r5sEVCUM", "expires_at": **********000}, {"width": 781, "height": 781, "url": "https://media.licdn.com/dms/image/v2/D5603AQF_WXTe2TucrA/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1728453592823?e=**********&v=beta&t=j-G9DEx2p63FDtVBjBSZh4MSLo8MEBZmtP8ePX_K98c", "expires_at": **********000}], "cover": [{"width": 800, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4E16AQFmkA-r0bMx3g/profile-displaybackgroundimage-shrink_200_800/profile-displaybackgroundimage-shrink_200_800/0/1674075317668?e=**********&v=beta&t=3W6bx3z0zFCHXl1JwNjo0NFQrZ-9J5jvqOwtK-cjm5g", "expires_at": **********000}, {"width": 1080, "height": 270, "url": "https://media.licdn.com/dms/image/v2/D4E16AQFmkA-r0bMx3g/profile-displaybackgroundimage-shrink_350_1400/profile-displaybackgroundimage-shrink_350_1400/0/1674075317668?e=**********&v=beta&t=7KoMDhIND7EW10nTxNELWD3MnkdKOHCPDLI7-yFuxkY", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Sr.bench sales recruiter", "location": "Houston, Texas, United States", "date": {"start": "Jul 2024", "end": "Present"}, "company": {"id": "89878118", "name": "Giga Cloud Services", "url": "https://www.linkedin.com/company/89878118/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQEKAU6ioul9yw/company-logo_200_200/company-logo_200_200/0/1708325921756/giga_cloud_services_logo?e=**********&v=beta&t=w-3a9Il7Fj2EAgPmAgbSv0w9sSTOTXz893BM_InjW9k", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQEKAU6ioul9yw/company-logo_100_100/company-logo_100_100/0/1708325921756/giga_cloud_services_logo?e=**********&v=beta&t=IT7CGCC1gy8wDP_V5y6PhdSFPg-m3klUIIzDg2hEmaM", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQEKAU6ioul9yw/company-logo_400_400/company-logo_400_400/0/1708325921756/giga_cloud_services_logo?e=**********&v=beta&t=Cge03EgmRUS1tq2u2m9aGDAOT75k92_pC2DwzLNuGsY", "expires_at": **********000}]}}, {"title": "Bench Sales Recruiter", "description": "Skills: Recruiting · Communication · Teamwork · Team Management · Staffing Services", "date": {"start": "Jan 2023", "end": "Jul 2024"}, "company": {"id": "89878118", "name": "Giga Cloud Services", "url": "https://www.linkedin.com/company/89878118/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQEKAU6ioul9yw/company-logo_200_200/company-logo_200_200/0/1708325921756/giga_cloud_services_logo?e=**********&v=beta&t=w-3a9Il7Fj2EAgPmAgbSv0w9sSTOTXz893BM_InjW9k", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQEKAU6ioul9yw/company-logo_100_100/company-logo_100_100/0/1708325921756/giga_cloud_services_logo?e=**********&v=beta&t=IT7CGCC1gy8wDP_V5y6PhdSFPg-m3klUIIzDg2hEmaM", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQEKAU6ioul9yw/company-logo_400_400/company-logo_400_400/0/1708325921756/giga_cloud_services_logo?e=**********&v=beta&t=Cge03EgmRUS1tq2u2m9aGDAOT75k92_pC2DwzLNuGsY", "expires_at": **********000}]}}], "skills": [{"skill": "Staffing Services"}, {"skill": "Communication"}, {"skill": "Teamwork"}, {"skill": "Team Management"}, {"skill": "Recruiting"}], "certifications": [], "publications": [], "educations": [{"school": "Avanthi Institute of Engineering Technology", "date": {"start": "Jun 2019", "end": "Sep 2022"}, "degree": "Bachelor of Technology - BTech, Mechanical Engineering"}, {"school": "TRR COLLEGE OF <PERSON>NG<PERSON>EERING AND TECHNOLOGY", "date": {"start": "Jun 2016", "end": "Apr 2019"}, "degree": "Diploma of Education, Mechanical Engineering"}, {"school": "word and deed high school", "date": {"start": "Apr 2016"}, "degree": "ssc"}], "honors": [], "volunteers": []}, {"id": "1100138811", "urn": "ACoAAEGSyTsBdFTTTEX4mPBH4nFhVfmNkBXcVbs", "public_identifier": "abhinava-shourya-4a3526268", "first_name": "<PERSON><PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "full_name": "<PERSON><PERSON><PERSON><PERSON>", "headline": "Bench Sales Recruiter", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": 10, "month": 8, "year": null}, "pronoun": null, "created": 1677739950208, "created_date": "2023-03-02T06:52:30.208Z", "location": {"country": "India", "country_code": "IN", "city": "Hyderabad, Telangana, India", "postal_code": null}, "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [], "skills": [], "certifications": [], "publications": [], "educations": [], "honors": [], "volunteers": []}, {"id": "947683231", "urn": "ACoAADh8f58BaqWRa9UmFvAD9eeIf23PBIGpN-U", "public_identifier": "subramanyam-sayam-967463225", "first_name": "Subramanyam", "last_name": "<PERSON><PERSON>", "full_name": "<PERSON><PERSON><PERSON><PERSON>", "headline": "Rec<PERSON>er", "is_premium": false, "is_open_to_work": true, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": true, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1636571162734, "created_date": "2021-11-10T19:06:02.734Z", "location": {"country": "India", "country_code": "IN", "city": "Hyderabad, Telangana, India", "postal_code": null}, "associated_hashtag": [], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Rec<PERSON>er", "location": "On-site", "date": {"start": "May 2024", "end": "May 2025"}, "employment_type": "Full-time", "company": {"id": "3195038", "name": "I28 Technologies Corporation", "url": "https://www.linkedin.com/company/3195038/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C510BAQGyeuOg0L355w/company-logo_200_200/company-logo_200_200/0/1631327283580?e=**********&v=beta&t=rP2F8fkGRr5hA0vOfoRB45caXgUhTp-_fmNCgUEC02Q", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C510BAQGyeuOg0L355w/company-logo_100_100/company-logo_100_100/0/1631327283580?e=**********&v=beta&t=C8oTDcsLHCemtnlYgBPn9RW8unX3yuChsEn_7aaPuTw", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C510BAQGyeuOg0L355w/company-logo_400_400/company-logo_400_400/0/1631327283580?e=**********&v=beta&t=KIqe2Mdnnpci0GuYnRVPalpTKSvph6gW-0SQXFpQQa8", "expires_at": **********000}]}}, {"title": " Sr. <PERSON>ch sales recuiter", "location": "On-site", "date": {"start": "Jan 2023", "end": "May 2024"}, "employment_type": "Full-time", "company": {"id": "35557624", "name": "Tek Leaders IT Staffing", "url": "https://www.linkedin.com/company/35557624/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGKy7HyAfa9rw/company-logo_200_200/company-logo_200_200/0/1630604896148?e=**********&v=beta&t=F5NRqXVue4Lt31_3InwxT9ndQT0vzPbd1EEbFHUOQUM", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGKy7HyAfa9rw/company-logo_100_100/company-logo_100_100/0/1630604896148?e=**********&v=beta&t=Kps6aD5cQpTRmw-SXCCxGiVdkjSKYDASOCaHVO8_ck4", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGKy7HyAfa9rw/company-logo_400_400/company-logo_400_400/0/1630604896148?e=**********&v=beta&t=AdpUfwv44hJSGvNnIQDjy8oOYtxL79Ky7hG2EB_1mbY", "expires_at": **********000}]}}, {"title": "Bench sales recruiter", "location": "Telangana, India · On-site", "date": {"start": "May 2020", "end": "Dec 2022"}, "employment_type": "Full-time", "company": {"id": "65403586", "name": "Arrixon Tech", "url": "https://www.linkedin.com/company/65403586/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGegri7cV3j2A/company-logo_200_200/company-logo_200_200/0/1630572086966?e=**********&v=beta&t=hD2ri5XqRJ2s1YX-hPhNypmQh6glrwlnhN0UYDcb8Sc", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGegri7cV3j2A/company-logo_100_100/company-logo_100_100/0/1630572086966?e=**********&v=beta&t=O6Y7wljYDDt8EuhH5sJBsZqMno6lTcfesZM7c79e6aA", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGegri7cV3j2A/company-logo_400_400/company-logo_400_400/0/1630572086966?e=**********&v=beta&t=R7ByIyoMT8V6I6clG7r67PKtYG8whGsCVrFPOT2AIXY", "expires_at": **********000}]}}], "skills": [], "certifications": [], "publications": [], "educations": [], "honors": [], "volunteers": []}, {"id": "1147456691", "urn": "ACoAAERkzLMBpljO3GXco8gIGvwYaJc1RdOgJKw", "public_identifier": "mathew-jason-52b345280", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON>", "full_name": "<PERSON><PERSON>", "headline": "Sr.<PERSON>ch sales recruiter", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": 17, "month": 10, "year": null}, "pronoun": null, "created": 1687424401371, "created_date": "2023-06-22T09:00:01.371Z", "location": {"country": "United States", "country_code": "US", "city": "Charlotte, North Carolina, United States", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D5603AQFE4NC3FjMgiA/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/1725967535109?e=**********&v=beta&t=-_pC0mJn8HIOhqW7DQXol3ClqOUVsFIcQT4svO_zJDM", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5603AQFE4NC3FjMgiA/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1725967535109?e=**********&v=beta&t=05aPumLLeYfM4SriWcQXLDatY_D_04V7rgkgnnjkkQQ", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D5603AQFE4NC3FjMgiA/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1725967535109?e=**********&v=beta&t=0bgCRp9vXkN8W0q1uGxx29KQeBd1m0o8oiJplI947Ss", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D5603AQFE4NC3FjMgiA/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1725967535109?e=**********&v=beta&t=YLZHfMdIb_59WNPl09LnWFA6stytDBSja8H8SJLKeT0", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Bench sales recruiter ", "location": "Hyderabbad,Telangana, India · On-site", "date": {"start": "Sep 2021", "end": "Present"}, "employment_type": "Full-time", "company": {"id": "90590411", "name": "Soft Tree Inc", "url": "https://www.linkedin.com/company/90590411/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C560BAQGCZQlYUTBcfA/company-logo_200_200/company-logo_200_200/0/1677771076457?e=**********&v=beta&t=lNNY7xZx2UDfu5OAfyk3oMHKjurvxP1uEfAQr_Yj-34", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C560BAQGCZQlYUTBcfA/company-logo_100_100/company-logo_100_100/0/1677771076457?e=**********&v=beta&t=bHkUB5JnLyuBo4QKPd8fd14icNzZK6-EiiDRBXrKu00", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C560BAQGCZQlYUTBcfA/company-logo_400_400/company-logo_400_400/0/1677771076457?e=**********&v=beta&t=W1Xf-sn14tCoMsBI5HcAH3J90pCh0TFk6ydMr7PhYq4", "expires_at": **********000}]}}], "skills": [], "certifications": [], "publications": [], "educations": [{"school": "Andhra University", "date": {}, "degree": "Master's degree"}], "honors": [], "volunteers": []}, {"id": "1021561023", "urn": "ACoAADzjyL8BHQPz8-lGYkK3tuP4dngyRlnHQp8", "public_identifier": "lokesh-mudiraj-*********", "first_name": "LOKESH", "last_name": "MUDIRAJ", "full_name": "LOKESH MUDIRAJ", "headline": "Lead Sales Recruiter | Expert in IT Staffing | Connecting Top Talent with Top Companies", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": 21, "month": 5, "year": null}, "pronoun": null, "created": 1658342100491, "created_date": "2022-07-20T18:35:00.491Z", "location": {"country": "United States", "country_code": "US", "city": "Rock Hill, South Carolina, United States", "postal_code": null}, "avatar": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5603AQGKKZktVQJTNA/profile-displayphoto-shrink_200_200/B56ZXY_JtmGcAg-/0/1743102189562?e=**********&v=beta&t=xKbwjU8dPdljZvJ89XDWSWmVijMe-eoEFj6XIYSENMc", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D5603AQGKKZktVQJTNA/profile-displayphoto-shrink_800_800/B56ZXY_JtmGcAk-/0/1743102189562?e=**********&v=beta&t=GswnxzgU2WBa4wxxK6wnxL3JEzfZYJIuCikViB5SmTY", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D5603AQGKKZktVQJTNA/profile-displayphoto-shrink_400_400/B56ZXY_JtmGcAo-/0/1743102189562?e=**********&v=beta&t=fbkrtCL5s9PIf_j8WFWafnNI_zO0PEq0u43RUiCpPqM", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D5603AQGKKZktVQJTNA/profile-displayphoto-shrink_100_100/B56ZXY_JtmGcAc-/0/1743102189551?e=**********&v=beta&t=TImxe88AFDPCXDU0bx7NxZV5-hqBK4TMCFXMVI56cuY", "expires_at": **********000}], "cover": [{"width": 800, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5616AQGQWGhQViWrKg/profile-displaybackgroundimage-shrink_200_800/profile-displaybackgroundimage-shrink_200_800/0/1732308019531?e=**********&v=beta&t=Th0Oab0YT88bmrY3ZxCK5jNiEKj_pczJzmH9-NLuwNM", "expires_at": **********000}, {"width": 1400, "height": 350, "url": "https://media.licdn.com/dms/image/v2/D5616AQGQWGhQViWrKg/profile-displaybackgroundimage-shrink_350_1400/profile-displaybackgroundimage-shrink_350_1400/0/1732308019531?e=**********&v=beta&t=ZjRbCVyQ1ZOgDsxURagAtfIY3D4C4ButCfBRITZEOOs", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Bench Sales Recruiter", "description": "✅Updating the resume as per Job Description and submitting it to vendor to get Interviews.  \n✅Negotiated on rates with the vendors as suggested by management based upon location and duration.\n✅Keeping a track record of rate confirmation and making follow ups about the submissions.\n✅Maintained a track progress of each and every submittal during the process.\n✅Follow up with the vendors on interviews schedules and coordinated between consultants and vendors.", "location": "Rock Hill, SC, USA", "date": {"start": "Feb 2024", "end": "Present"}, "employment_type": "Full-time", "skills": ["Bench Sales Recruiting", "Vendor & Client Management", "Market Research and Analysis", "Rate Negotiation", "US Tax Terms (W2, C2C, 1099)"], "company": {"id": "11515557", "name": "Caliber IT Solutions Inc", "url": "https://www.linkedin.com/company/11515557/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C560BAQGQNiX3b87GBw/company-logo_200_200/company-logo_200_200/0/1630646989253/caliber_it_solutions_inc_logo?e=**********&v=beta&t=lbkrP4UI4fnic7dKFgxhSnO5dHEMfC0QyQvicv8TysM", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C560BAQGQNiX3b87GBw/company-logo_100_100-alternative/company-logo_100_100-alternative/0/1630646989279/caliber_it_solutions_inc_logo?e=**********&v=beta&t=gD1SKlxreXAqqiy7mtqP_uCjAbiQdmKEOIW5LJPhp8s", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C560BAQGQNiX3b87GBw/company-logo_200_200-alternative/company-logo_200_200-alternative/0/1630646989279/caliber_it_solutions_inc_logo?e=**********&v=beta&t=rDPsj4FpBwuMA6iwRdAv1MGO5TMzZjFAbobbGWfxjrk", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C560BAQGQNiX3b87GBw/company-logo_100_100/company-logo_100_100/0/1630646989253/caliber_it_solutions_inc_logo?e=**********&v=beta&t=yXmRqkUbTBAESqyTozR3H8CTfcX3anuEJkUcjpavjCk", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C560BAQGQNiX3b87GBw/company-logo_400_400-alternative/company-logo_400_400-alternative/0/1630646989279/caliber_it_solutions_inc_logo?e=**********&v=beta&t=lNYU89rZ5gbBT0WwDnjYSB7Jq7SwJ75AdWshJkimFEU", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C560BAQGQNiX3b87GBw/company-logo_400_400/company-logo_400_400/0/1630646989253/caliber_it_solutions_inc_logo?e=**********&v=beta&t=hUhChl62b6BDdpU5wwUHz7LXuDmkGp7N5PLgptKpA5s", "expires_at": **********000}]}, "media": [{"description": null, "thumbnail": [{"width": 160, "height": 113, "url": "https://media.licdn.com/dms/image/v2/D562DAQGL111dCUMvqg/profile-treasury-image-shrink_160_160/B56ZXYQO4oGUAw-/0/1743089889892?e=**********&v=beta&t=K8GS02B6A4AgW3uuE37Bk8IYtQ6MEXXotXhto-pnPUk", "expires_at": **********000}, {"width": 1600, "height": 1137, "url": "https://media.licdn.com/dms/image/v2/D562DAQGL111dCUMvqg/profile-treasury-image-shrink_8192_8192/B56ZXYQO4oGUAs-/0/1743089889892?e=**********&v=beta&t=BKj1K5gejFIFh4iwhq7jzsDVajVWKtfLvj4U-Dl9Pso", "expires_at": **********000}, {"width": 1280, "height": 909, "url": "https://media.licdn.com/dms/image/v2/D562DAQGL111dCUMvqg/profile-treasury-image-shrink_1280_1280/B56ZXYQO4oGUAc-/0/1743089889892?e=**********&v=beta&t=ImiTa0ucrKYBLW270S9Hjr8HcleJjdIE7MX7kS3ezrU", "expires_at": **********000}, {"width": 1600, "height": 1137, "url": "https://media.licdn.com/dms/image/v2/D562DAQGL111dCUMvqg/profile-treasury-image-shrink_1920_1920/B56ZXYQO4oGUAo-/0/1743089889935?e=**********&v=beta&t=ChvGi5DNOqmhPedDCSVpJjk1QBDrdQ4dqkilXIkSYKc", "expires_at": **********000}, {"width": 480, "height": 341, "url": "https://media.licdn.com/dms/image/v2/D562DAQGL111dCUMvqg/profile-treasury-image-shrink_480_480/B56ZXYQO4oGUAY-/0/1743089889935?e=**********&v=beta&t=60AeT3n5OyTv2w1gjXelvlG5Al5zcHxmOWmIYVG8QNc", "expires_at": **********000}, {"width": 800, "height": 568, "url": "https://media.licdn.com/dms/image/v2/D562DAQGL111dCUMvqg/profile-treasury-image-shrink_800_800/B56ZXYQO4oGUAk-/0/1743089889935?e=**********&v=beta&t=SP8D4Wu4I61VxZBzZUJE34kM0uon3qhcPAY2GNReMHo", "expires_at": **********000}], "title": "BEST PERFORMER FEB 2025"}]}, {"title": "Bench Sales Recruiter", "description": "✅Posting resumes on behalf of consultants in to various job portals like Dice, Monster, Indeed, etc.\n✅Regular follow ups with vendors on the feedbacks and interviews\n✅Negotiating rates and getting the best possible rates for consultants.\n✅Organizes feedback sessions on interviewed candidates.\n✅Mentor the team to achieve targets and eventually grow with the organization.\n✅Keep track of all candidates, submissions, and interviews in a database.", "location": "McKinney, Texas, USA", "date": {"start": "Jul 2022", "end": "Jan 2024"}, "employment_type": "Full-time", "skills": ["Bench Sales Recruiting", "IT Consulting", "Vendor & Client Management", "Market Research and Analysis", "Rate Negotiation", "US Tax Terms (W2, C2C, 1099)"], "company": {"id": "82879074", "name": "CIS TECHNOLOGIES INC", "url": "https://www.linkedin.com/company/82879074/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQH9Wth64PcSzA/company-logo_200_200/company-logo_200_200/0/1687468234581/cis_technologies_inc_logo?e=**********&v=beta&t=-sY4PZlRQpDzxXzf2dKCxSu5Wsd489l3507rOA1LAQE", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQH9Wth64PcSzA/company-logo_100_100/company-logo_100_100/0/1687468234581/cis_technologies_inc_logo?e=**********&v=beta&t=labClmcl7-UHgf8JWC92qynRezN4n-TEHx640ZQc520", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQH9Wth64PcSzA/company-logo_400_400/company-logo_400_400/0/1687468234581/cis_technologies_inc_logo?e=**********&v=beta&t=WqqZoF1VpChIiP0Jqt-dr2kD_rCGmcyZvyoEFFnXm_Y", "expires_at": **********000}]}}], "skills": [{"skill": "US Tax Terms (W2, C2C, 1099)"}, {"skill": "Bench Sales Recruiting"}, {"skill": "IT Consulting"}, {"skill": "Vendor & Client Management"}, {"skill": "Rate Negotiation"}, {"skill": "Vendor and Client Management", "num_endorsements": 1}, {"skill": "Market Research and Analysis", "num_endorsements": 1}, {"skill": "Effective Communication", "num_endorsements": 1}, {"skill": "Negotiation Skills", "num_endorsements": 1}, {"skill": "Time Management and Multitasking", "num_endorsements": 1}, {"skill": "Vendor Management", "num_endorsements": 1}, {"skill": "Sales Recruitment", "num_endorsements": 1}, {"skill": "Sales Strategy", "num_endorsements": 1}, {"skill": "Relationship Building", "num_endorsements": 1}, {"skill": "Contract Recruitment", "num_endorsements": 1}, {"skill": "Sourcing"}, {"skill": "Recruiting"}, {"skill": "Staffing Services", "num_endorsements": 1}], "certifications": [], "publications": [], "educations": [{"school": "Kendriya Vidyalaya", "date": {}, "degree": "CBSC"}, {"school": "Osmania University, Hyderabad", "date": {}, "degree": "B.com computers , Computer/Information Technology Administration and Management"}], "honors": [], "volunteers": []}, {"id": "977931030", "urn": "ACoAADpKCxYBumXpIosJ4c4OSeml0HtDWEFi6Ds", "public_identifier": "pravin-b<PERSON>yar-386610233", "first_name": "PRAVIN", "last_name": "BHOYAR", "full_name": "PRAVIN BHOYAR", "headline": "US IT Recruiter | Talent Acquisition at Datasys Consulting and Software Inc.", "is_premium": false, "is_open_to_work": false, "is_hiring": true, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": true, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1646243901867, "created_date": "2022-03-02T17:58:21.867Z", "location": {"country": "United States", "country_code": "US", "city": "Princeton, New Jersey, United States", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D5603AQGb-XEU7mMxdQ/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/1720550229710?e=**********&v=beta&t=lAzq6872F2Qu3R8dCNguA4nXPsRhtL1FHYGcp-gdciI", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5603AQGb-XEU7mMxdQ/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1720550229710?e=**********&v=beta&t=b0BhMPNkBuxZ-esWGKuUZQGV5AN-KmxN6Tcuz1IHh54", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D5603AQGb-XEU7mMxdQ/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1720550229710?e=**********&v=beta&t=DClwM4Ao8N4_BuRzl1nP3rIIQThWtEBujjenx8MmRyI", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D5603AQGb-XEU7mMxdQ/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1720550229735?e=**********&v=beta&t=pAlh6UBXc2bWzD4_3HOpZQdi3Dk_CSXYc13ehlME_gI", "expires_at": **********000}], "associated_hashtag": [], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Technical Recruiter | Talent Acquisition", "location": "Princeton, New Jersey, United States · Remote", "date": {"start": "Jan 2024", "end": "Present"}, "employment_type": "Full-time", "company": {"id": "5156208", "name": "Datasys Consulting and Software Inc.", "url": "https://www.linkedin.com/company/5156208/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGL1hsr082WSA/company-logo_200_200/company-logo_200_200/0/1631329265952?e=**********&v=beta&t=sPY2s7rFaUaR-3FfGfPLkYn5idnjlVNptgWkFskrdWo", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGL1hsr082WSA/company-logo_100_100/company-logo_100_100/0/1631329265952?e=**********&v=beta&t=BCGaqJT9ffIL-l9KB64LJWnyF1uKQhqUQD4zMQDy-k0", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGL1hsr082WSA/company-logo_400_400/company-logo_400_400/0/1631329265952?e=**********&v=beta&t=v6zNUTPquRYYOQ6ExlolX7d3UogY4vupTauJMt-H8UE", "expires_at": **********000}]}}, {"title": "Technical Recruiter", "description": "Contribution:\nRecruiting for US Candidates with Visa like H1B, GC-EAD, USC, OPT, CPT, TN Visa.\n• Recruitment for IT professionals for client companies based in US, conduct basic checks, and support till final placement of the candidate.\n• Permanent and contract position Corp to Corp, Contract to Hire (C2H), & W-2 basis and Implementations Partners.(IP)\n• Utilizing recruitment tools like Ceipal, Monster, Dice, Linkedin and other networking groups, personal database, reference and other non-conventional methods of resource.\n• Working on multiple positions by sourcing, screening, qualifying, coordinating interviews, negotiating and closing candidates. \n• Worked with various Clients like L&T, Deloitte, Meta, Infosys, Facebook, Lumen Technologies, T. Row Price, T- Mobile, etc. different States clients of US, more focused on Contract position.\n• Dealing with the vendors using mass mail.\n• Screening good candidates with nice skills and scheduling their interviews as per discussed with the clients.", "location": "Piscataway, New Jersey, United States · On-site", "date": {"start": "Jul 2022", "end": "Oct 2022"}, "employment_type": "Full-time", "skills": ["Screening", "Closing Candidates", "Full-life Cycle Recruiting", "Recruiting", "Sourcing"], "company": {"id": "11133123", "name": "Emonics LLC", "url": "https://www.linkedin.com/company/11133123/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQGXuMvXtEhsTg/company-logo_200_200/company-logo_200_200/0/1689151686539/emonics_logo?e=**********&v=beta&t=wC-ZGTjKDFr1QTetJZuF4hDa9GQ8feqKsgsv0dAk1_U", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQGXuMvXtEhsTg/company-logo_100_100/company-logo_100_100/0/1689151686539/emonics_logo?e=**********&v=beta&t=uon8tU7o_z3vqvfu9K2SujyjyKLxvdXXqUGmE5G2U8w", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQGXuMvXtEhsTg/company-logo_400_400/company-logo_400_400/0/1689151686539/emonics_logo?e=**********&v=beta&t=Hhesh81WEN3n-bEeyppGAY66Da6jJCHinAVvePabpIQ", "expires_at": **********000}]}}], "skills": [{"skill": "Job Postings"}, {"skill": "Closing Candidates"}, {"skill": "Full-life Cycle Recruiting"}, {"skill": "Sourcing", "num_endorsements": 1}, {"skill": "Recruiting"}, {"skill": "Technical Recruiting"}, {"skill": "Recruitment Advertising"}, {"skill": "IT Recruitment"}, {"skill": "Technology Recruitment"}, {"skill": "Boolean Searching"}, {"skill": "Screening"}, {"skill": "Direct Search"}, {"skill": "IT Sourcing"}, {"skill": "Salary Negotiations"}], "certifications": [], "publications": [], "educations": [{"school": "<PERSON><PERSON><PERSON><PERSON><PERSON>van Maharashtra Open University", "date": {"start": "2018", "end": "2021"}, "degree": "Bachelor of Arts - BA, Art/Art Studies, General", "description": "Skills: Closing Candidates", "skills": ["Closing Candidates"]}], "honors": [], "volunteers": []}, {"id": "*********", "urn": "ACoAADpVs68BQuv0Ps8RqUf88o-5hyFWtMZvC7U", "public_identifier": "shravani-nayakam-57b91a233", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Nayakam", "full_name": "<PERSON><PERSON><PERSON>", "headline": "HR Recruiter at SQA Concepts Inc", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1646418920708, "created_date": "2022-03-04T18:35:20.708Z", "location": {"country": "India", "country_code": "IN", "city": "Hyderabad, Telangana, India", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D4D03AQFpWzoswIniaQ/profile-displayphoto-shrink_100_100/B4DZbF4RfBGUAU-/0/1747076586586?e=**********&v=beta&t=n0-g9jTRFi5112mBOkZt3sA-QIRDzJdMnmS4W_ri2YA", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D4D03AQFpWzoswIniaQ/profile-displayphoto-shrink_800_800/B4DZbF4RfBGUAc-/0/1747076586586?e=**********&v=beta&t=1phXzW1zfZbvyST3A5A-JTE-Mwk6H-gzizZJDUJaiyA", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D03AQFpWzoswIniaQ/profile-displayphoto-shrink_200_200/B4DZbF4RfBGUAY-/0/1747076586586?e=**********&v=beta&t=ah3OPKe1v49WK_3KPh6Y31QfBsKqb4EnQe6fOphbS50", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D4D03AQFpWzoswIniaQ/profile-displayphoto-shrink_400_400/B4DZbF4RfBGUAg-/0/1747076586555?e=**********&v=beta&t=b4SfW5pFK4Ijd-72vc9bYE3D2oRWc4q7Hg4vain7pOA", "expires_at": **********000}], "cover": [{"width": 800, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D16AQGZs6bKkf9E-w/profile-displaybackgroundimage-shrink_200_800/profile-displaybackgroundimage-shrink_200_800/0/1733178684631?e=**********&v=beta&t=Kqrb1A3UPTX2XGP34WqXaUwbGJ3mvcaX5OoaXWUnZeo", "expires_at": **********000}, {"width": 1080, "height": 270, "url": "https://media.licdn.com/dms/image/v2/D4D16AQGZs6bKkf9E-w/profile-displaybackgroundimage-shrink_350_1400/profile-displaybackgroundimage-shrink_350_1400/0/1733178684631?e=**********&v=beta&t=hQOIWZjQ5_ob7s46qCHeVAqGieya2CNzvW-gOd0lspA", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "HR Recruiter", "description": "Roles and responsibilities: \n. Handling End-to-end recruitment with the ability to manage multiple requirements at a time.\n• Conducting Initial screening to check the candidate's suitability in attitude, academics, professional qualification, experience, communication skills, etc. before processing.\n• Candidate should be well versed with Job portals like; Naukri, Linkedin, and other job portals to find the right talent.\n• Following up with shortlisted candidates for joining dates and convincing them, if they have any issues such as compensation review/ relocation/onsite travel etc.\n• Excellent written and verbal English communication skills.\n• Onboarding formalities, Induction, Data management, and maintaining the personal file.\n .To handle end-to-end recruitment activities (screening, sourcing, searching, conducting interviews, administering tests, roll out, shortlisting, etc.). Active involvement in sourcing candidates from various references.\n • Use social media, job boards, Internet sourcing, and other technical Teams to source candidates for open jobs.\n • Initiating and overseeing the hiring, training, and dismissal processes and administering and monitoring benefit programs.\n • Completing joining formalities for new joiners. Ensuring smooth on-boarding of new joiners including induction and team introduction.\n.Experience working with H1B Consultants.\n.Ability to interact, develop tier-1 Vendor network and get the H1B candidates placed in minimal turnaround time.\n.Experienced with End-to-end cycle of sales from submitting Profiles, Rate Negotiations, and Follow-up.\n.Knowledge of Employment Type Corp to Corp.\n.Knowledge of Job Portals like Dice, Monster, Linked In, and social media Platforms.\n.Knowledge of visa classification Terms, Rules, and policies for H1 B  visa.\n.Looking for someone aggressive, a Team Player, and well aware of the technologies in the market.", "date": {"start": "Jun 2023", "end": "Present"}, "company": {"id": "1725133", "name": "SQA Concepts Inc", "url": "https://www.linkedin.com/company/1725133/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQHYbHnZgsTguA/company-logo_200_200/company-logo_200_200/0/1630572231101/sqa_concepts_inc_logo?e=**********&v=beta&t=1fRCuvCzLtFdjrr_15LxZzJT1-Yn-5D1iuoeTA01TTY", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQHYbHnZgsTguA/company-logo_100_100/company-logo_100_100/0/1630572231101/sqa_concepts_inc_logo?e=**********&v=beta&t=kAJAuUo0r11Zr9U99Hl0gZMors1YpBuDQbEUyZ743cY", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQHYbHnZgsTguA/company-logo_400_400/company-logo_400_400/0/1630572231101/sqa_concepts_inc_logo?e=**********&v=beta&t=jzheEQ-iakEspzNagdVCRrIVXCKWu9l9KFy5GvK-mAc", "expires_at": **********000}]}}, {"title": "HR Recruiter ", "date": {"start": "Jun 2023", "end": "May 2025"}, "employment_type": "Full-time", "company": {"id": "1725133", "name": "SQA Concepts Inc", "url": "https://www.linkedin.com/company/1725133/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQHYbHnZgsTguA/company-logo_200_200/company-logo_200_200/0/1630572231101/sqa_concepts_inc_logo?e=**********&v=beta&t=1fRCuvCzLtFdjrr_15LxZzJT1-Yn-5D1iuoeTA01TTY", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQHYbHnZgsTguA/company-logo_100_100/company-logo_100_100/0/1630572231101/sqa_concepts_inc_logo?e=**********&v=beta&t=kAJAuUo0r11Zr9U99Hl0gZMors1YpBuDQbEUyZ743cY", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQHYbHnZgsTguA/company-logo_400_400/company-logo_400_400/0/1630572231101/sqa_concepts_inc_logo?e=**********&v=beta&t=jzheEQ-iakEspzNagdVCRrIVXCKWu9l9KFy5GvK-mAc", "expires_at": **********000}]}}, {"title": "Information Technology Recruiter", "description": ">The recruitment process includes screening, Interviewing, selection, and candidate engagement. \n>Identifying the resumes that match the client's requirements, through various job portals, vendors, contacts, databases, and third parties and posting the requirements on the websites. \n>Hiring candidates. Scheduling interviews for candidates and sending the invites/following up with candidates. \n>Client handling, Onboarding candidates, and Offer negotiation with candidates.\n>Proactively sourced potential candidates through LinkedIn, Naukri, Networking, and other internet tools.\n>Providing feedback to the candidates after completing the interview.\n\nSkills: Scheduling · Onboarding · Client Onboarding · Sourcing", "date": {"start": "May 2022", "end": "Aug 2022"}, "company": {"name": "Kennevision technology pvt ltd", "url": "https://www.linkedin.com/search/results/all/?keywords=Kennevision+technology+pvt+ltd"}}, {"title": "Human resources recuriter", "description": "Human Resources Recruiter\n.Currently Working as HR Recruiter at Whatnxt career services\n.Trained as HR Intern at 2020", "location": "Siddipet, Telangana, India", "date": {"start": "Sep 2020", "end": "Mar 2022"}, "company": {"name": "whatnxt career services ,hyd", "url": "https://www.linkedin.com/search/results/all/?keywords=whatnxt+career+services+%2Chyd"}}], "skills": [{"skill": "Recruiting"}, {"skill": "HR Recruitment"}, {"skill": "Pencil arts"}, {"skill": "LinkedIn Recruiter"}, {"skill": "Microsoft Office"}, {"skill": "Microsoft Excel"}, {"skill": "Microsoft Word"}, {"skill": "Teamwork"}, {"skill": "Hiring Trends"}, {"skill": "HR Management"}], "certifications": [], "publications": [], "educations": [{"school": "Indur institute of engineering and technology", "date": {"start": "2018", "end": "2020"}, "degree": "Master of Business Administration - MBA, Human Resources Management/Personnel Administration, General", "grade": "A"}], "honors": [], "volunteers": [{"organization": "Whatnxt career services", "title": "Having a hands-on experience in HR  & as a BSR since more than a year.", "duration": "4 yrs 10 mos", "description": "Talks about  #sourcing #screening #hiringprocess #endtoendrecruitment #itrecruiting #recruitmentprocess #schedulinginterwies #shortlistingcandidates #onboardingcandidates", "date": {"start": "Sep 2020", "end": "Present"}}, {"organization": "SQA Concepts Inc", "title": "Human Resources Recruiter", "duration": "2 yrs", "date": {"start": "Jul 2023", "end": "Present"}}, {"organization": "Kennevision Technologies Pvt Ltd", "title": "Human Resources Recruiter", "duration": "4 mos", "date": {"start": "Aug 2022", "end": "Nov 2022"}}]}, {"id": "1032610727", "urn": "ACoAAD2MY6cBeyAjc0gOPxkq9hldPkMWPC0ztX4", "public_identifier": "bhuvana-ramarao-79b99b249", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "full_name": "<PERSON><PERSON><PERSON>", "headline": "Sr.Bench Sales Recruiter", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1661340957282, "created_date": "2022-08-24T11:35:57.282Z", "location": {"country_code": "US", "city": "United States", "postal_code": null}, "cover": [{"width": 800, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5616AQHwNMFnv1xpFQ/profile-displaybackgroundimage-shrink_200_800/profile-displaybackgroundimage-shrink_200_800/0/1682631970881?e=**********&v=beta&t=Ui9m6EVqqA5-foWc8yM7pvNU7GnXzhNhn_CGBb7b3q4", "expires_at": **********000}, {"width": 1080, "height": 270, "url": "https://media.licdn.com/dms/image/v2/D5616AQHwNMFnv1xpFQ/profile-displaybackgroundimage-shrink_350_1400/profile-displaybackgroundimage-shrink_350_1400/0/1682631970881?e=**********&v=beta&t=9jDMKSBGFdpWXrUFuAd6j7UeXijL9mEr0Vz3inveGho", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Sr. Bench Sales Recruiter", "location": " Austin,TX-78731.", "date": {"start": "Sep 2024", "end": "Present"}, "company": {"id": "*********", "name": "Dataforge Technologies", "url": "https://www.linkedin.com/company/*********/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQGygyM01JI-mw/company-logo_200_200/company-logo_200_200/0/1719257066318?e=**********&v=beta&t=gNbMNYf5PgRGX6QTCdlDIGOvW5dIhE_kHdgZOhoc3CE", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQGygyM01JI-mw/company-logo_100_100/company-logo_100_100/0/1719257667765?e=**********&v=beta&t=lx8-r6_akc7mVCjGsbkqbh2pWG4Yplc8tS54QTJB_GQ", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQGygyM01JI-mw/company-logo_400_400/company-logo_400_400/0/1719256661532?e=**********&v=beta&t=58xgGl_FEVzYTqf5F1VbgvjVSoDKOtuIjVgkQkKIBQo", "expires_at": **********000}]}}, {"title": "Bench Sales Recruiter ", "location": "United States", "date": {"start": "Mar 2023", "end": "Jan 2025"}, "company": {"id": "*********", "name": "Jolttek Inc", "url": "https://www.linkedin.com/company/*********/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQHV9CXWn1pKig/company-logo_200_200/company-logo_200_200/0/1719256446174?e=**********&v=beta&t=jA9q3zGC2XY6y5DaR1ThwOb-fEVnm6gB7qfLwJFTv-U", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQHV9CXWn1pKig/company-logo_100_100/company-logo_100_100/0/1719257770473?e=**********&v=beta&t=YL39fEi36Do-Rs_P28WtXLT2s7MDzZ74kmO6cXvGQo8", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQHV9CXWn1pKig/company-logo_400_400/company-logo_400_400/0/1719257662599?e=**********&v=beta&t=_TX-e6Y-chlCLPIRcTQ__gwp9E-i_jjsiEOp_v0HRKc", "expires_at": **********000}]}}, {"title": "Bench Sales Recruiter ", "location": "Newark, Delaware, United States", "date": {"start": "Feb 2022", "end": "Jan 2025"}, "employment_type": "Full-time", "company": {"id": "*********", "name": "Jolttek Inc", "url": "https://www.linkedin.com/company/*********/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQHV9CXWn1pKig/company-logo_200_200/company-logo_200_200/0/1719256446174?e=**********&v=beta&t=jA9q3zGC2XY6y5DaR1ThwOb-fEVnm6gB7qfLwJFTv-U", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQHV9CXWn1pKig/company-logo_100_100/company-logo_100_100/0/1719257770473?e=**********&v=beta&t=YL39fEi36Do-Rs_P28WtXLT2s7MDzZ74kmO6cXvGQo8", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQHV9CXWn1pKig/company-logo_400_400/company-logo_400_400/0/1719257662599?e=**********&v=beta&t=_TX-e6Y-chlCLPIRcTQ__gwp9E-i_jjsiEOp_v0HRKc", "expires_at": **********000}]}}], "skills": [], "certifications": [], "publications": [], "educations": [{"school": "Kakatiya University", "date": {"start": "2017", "end": "2020"}, "degree": "B.com computers , Commerce "}], "honors": [], "volunteers": []}, {"id": "*********", "urn": "ACoAADaXQA8BFhG7IqtocAMA4j3-AnR99kx5pr8", "public_identifier": "chandrika-ariveni-37b888216", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "full_name": "<PERSON><PERSON><PERSON>", "headline": "Bench sales recuiter", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": 24, "month": 9, "year": null}, "pronoun": null, "created": 1625674243431, "created_date": "2021-07-07T16:10:43.431Z", "location": {"country_code": "US", "city": "United States", "postal_code": null}, "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Bench sales recruiter", "description": "Skills: Sales Recruitment · Multitasking", "location": "Charlotte, North Carolina, United States · On-site", "date": {"start": "Feb 2024", "end": "Present"}, "employment_type": "Full-time", "company": {"id": "3321547", "name": "Neo Prism Solutions LLC", "url": "https://www.linkedin.com/company/3321547/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQHO_2kWQfa6Hw/company-logo_200_200/company-logo_200_200/0/1666246799545/neo_prism_solutions_logo?e=**********&v=beta&t=ZXpDSpjYaHnmspriZv60dsV3b-ZlkTR9D5cYzLKVzvQ", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQHO_2kWQfa6Hw/company-logo_100_100/company-logo_100_100/0/1666246799545/neo_prism_solutions_logo?e=**********&v=beta&t=ca6z_ZjzeiWfb5gfs8NMGnFriPu8pw24-U8r5dBTrIo", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQHO_2kWQfa6Hw/company-logo_400_400/company-logo_400_400/0/1666246799545/neo_prism_solutions_logo?e=**********&v=beta&t=KClL6SGM8MhrWsvSGYiFrc6A69KLGiFLKqJil11_uBk", "expires_at": **********000}]}}, {"title": "Talent Acquisition Specialist", "description": "Skills: Technical Recruiting · Global Talent Acquisition", "location": "Dallas, Texas, United States · On-site", "date": {"start": "Aug 2021", "end": "Jan 2024"}, "employment_type": "Full-time", "company": {"id": "14732168", "name": "Artifint Technologies Private Limited", "url": "https://www.linkedin.com/company/14732168/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C510BAQFZIrKrjKom5w/company-logo_200_200/company-logo_200_200/0/1630576398692?e=**********&v=beta&t=fR-MHGWCQDvblqABdL_SHM7gF73zNDKsygiWOirztqc", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C510BAQFZIrKrjKom5w/company-logo_100_100/company-logo_100_100/0/1630576398692?e=**********&v=beta&t=FDk6WZmRDLfsm61wyTPGu3BCyO7wDG9bzDgLlNDI3rQ", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C510BAQFZIrKrjKom5w/company-logo_400_400/company-logo_400_400/0/1630576398692?e=**********&v=beta&t=jRjplWqbwTkWxKwMMVz-j0jxZf3Vqw5YWAC9FrajvyU", "expires_at": **********000}]}}], "skills": [{"skill": "Multitasking"}, {"skill": "Sales Recruitment"}, {"skill": "Sourcing", "num_endorsements": 1}, {"skill": "Recruiting", "num_endorsements": 1}, {"skill": "Technical Recruiting"}, {"skill": "Global Talent Acquisition"}, {"skill": "Engineering", "num_endorsements": 1}, {"skill": "English", "num_endorsements": 1}, {"skill": "Analytical Skills"}, {"skill": "Education"}, {"skill": "Project Management"}, {"skill": "Communication"}], "certifications": [], "publications": [], "educations": [{"school": "International Women's School of Technology & Sciences", "date": {"start": "Jun 2018", "end": "Jun 2021"}, "degree": "B.tech, Electronics and communication engineering"}, {"school": "NOVA College of Engineering and Technology, Hyderabad", "date": {"start": "Aug 2015", "end": "Apr 2018"}, "degree": "Diploma of Education, Electrical, Electronics and Communications Engineering"}], "honors": [], "volunteers": []}, {"id": "959164584", "urn": "ACoAADkrsKgBrPd78yWmlnAzutth0l2LZJ4xcBs", "public_identifier": "bhanu-prasad-120280229", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON>", "full_name": "<PERSON><PERSON><PERSON>", "headline": "Bench Sales Recruiter at Indus group Inc", "is_premium": false, "is_open_to_work": true, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1640611856613, "created_date": "2021-12-27T13:30:56.613Z", "location": {"country": "India", "country_code": "IN", "city": "Hyderabad, Telangana, India", "postal_code": null}, "avatar": [{"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D5603AQH9A86D6ICpPg/profile-displayphoto-shrink_400_400/B56ZaAwelrHUAg-/0/1745916915691?e=**********&v=beta&t=dauF17FhdmmkA9Grsm1WwMkElmBiaM2XlsHawUU3yxI", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5603AQH9A86D6ICpPg/profile-displayphoto-shrink_200_200/B56ZaAwelrHUAY-/0/1745916915691?e=**********&v=beta&t=4pVb7zKjShrbGlvS4_POq_VBAIIS5ZbmN8nYAdGV0Hg", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D5603AQH9A86D6ICpPg/profile-displayphoto-shrink_800_800/B56ZaAwelrHUAc-/0/1745916915691?e=**********&v=beta&t=nW3S1ShiqYNNfJIQcnBK5DEgDHph43eawMRDzBr-a1w", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D5603AQH9A86D6ICpPg/profile-displayphoto-shrink_100_100/B56ZaAwelrHUAU-/0/1745916915516?e=**********&v=beta&t=MeYBgBfETeJmKH2ZpQEF7LFgQc5OIOz0lK26CFQeFP0", "expires_at": **********000}], "cover": [{"width": 744, "height": 186, "url": "https://media.licdn.com/dms/image/v2/D5616AQE7gPaxXqthvQ/profile-displaybackgroundimage-shrink_200_800/profile-displaybackgroundimage-shrink_200_800/0/1718650022002?e=**********&v=beta&t=4ZUBdFnAKcUUbBW6gnoUO0kQ52lcnAeKKwO_i72e64s", "expires_at": **********000}, {"width": 744, "height": 186, "url": "https://media.licdn.com/dms/image/v2/D5616AQE7gPaxXqthvQ/profile-displaybackgroundimage-shrink_350_1400/profile-displaybackgroundimage-shrink_350_1400/0/1718650022002?e=**********&v=beta&t=fVPyzit0FgkMgCM2-MFYEv--rpUbcMLtNcFiyTCgn-w", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "bench sales recruiter", "location": "On-site", "date": {"start": "Apr 2024", "end": "Present"}, "employment_type": "Full-time", "company": {"id": "7939193", "name": "Indus Group Inc", "url": "https://www.linkedin.com/company/7939193/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQEfMET6wAnQAg/company-logo_200_200/company-logo_200_200/0/1732232216256/indus_grp_logo?e=**********&v=beta&t=eZbURf--lZW-84TYYkZUofDYNOQIdsPTPE_ZnzXUN3I", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQEfMET6wAnQAg/company-logo_100_100/company-logo_100_100/0/1732232216256/indus_grp_logo?e=**********&v=beta&t=3drWdC4fPebQwO3BXLENTdkDSFzLUKRec_a_WTzil3w", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQEfMET6wAnQAg/company-logo_400_400/company-logo_400_400/0/1732232216256/indus_grp_logo?e=**********&v=beta&t=RaWDGfbF5GIjOi0HkSG38vGV_ZMomdZQZ4BfULKPYWE", "expires_at": **********000}]}}, {"title": "Sales Recruiter", "date": {"start": "Feb 2024", "end": "Mar 2024"}, "company": {"id": "74773165", "name": "WYE Consulting Services", "url": "https://www.linkedin.com/company/74773165/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQGu3C0dOy5FMw/company-logo_200_200/company-logo_200_200/0/1734445371004/wyeconsultingservices_logo?e=**********&v=beta&t=9zAvydZKp_r97xFQnI0ABRGEPHs0yiAxHny8_IoPI9g", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQGu3C0dOy5FMw/company-logo_100_100/company-logo_100_100/0/1734445371004/wyeconsultingservices_logo?e=**********&v=beta&t=qzFmZwOnSeLC0lsjLmzsb8hlIvtqamDKPVqxHtk0bDE", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQGu3C0dOy5FMw/company-logo_400_400/company-logo_400_400/0/1734445371004/wyeconsultingservices_logo?e=**********&v=beta&t=3KnBSm5F9r1oyRA06_nCcYT9ci4_kyqABPex7yU2kNs", "expires_at": **********000}]}}], "skills": [], "certifications": [], "publications": [], "educations": [], "honors": [], "volunteers": []}, {"id": "*********", "urn": "ACoAADaMkNEBubvyz1vPgdYUDL281_vkIXSMbGo", "public_identifier": "saisree-mula-1015ab216", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON>", "full_name": "<PERSON><PERSON><PERSON>", "headline": "Senior Bench Sales Recruiter", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1625492036930, "created_date": "2021-07-05T13:33:56.930Z", "location": {"country": "India", "country_code": "IN", "city": "Hyderabad, Telangana, India", "postal_code": null}, "cover": [{"width": 800, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5616AQHxBQ_iVEjimw/profile-displaybackgroundimage-shrink_200_800/profile-displaybackgroundimage-shrink_200_800/0/1707422500711?e=**********&v=beta&t=I6-p8J9OtK_gDXgSLZeDeYgrHgHaE9IUVI5ngqU-16s", "expires_at": **********000}, {"width": 1400, "height": 350, "url": "https://media.licdn.com/dms/image/v2/D5616AQHxBQ_iVEjimw/profile-displaybackgroundimage-shrink_350_1400/profile-displaybackgroundimage-shrink_350_1400/0/1707422500711?e=**********&v=beta&t=qjSsoJS7EbuQol_Fcn41-0Czku-SGz48F0I_UA4jopg", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Senior Bench Sales Recruiter ", "location": "Herndon, Virginia, United States · On-site", "date": {"start": "Oct 2024", "end": "Present"}, "employment_type": "Full-time", "company": {"id": "9389246", "name": "SSA Tech Inc", "url": "https://www.linkedin.com/company/9389246/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C560BAQHyOt_wiEzzpw/company-logo_200_200/company-logo_200_200/0/1631337789085?e=**********&v=beta&t=EB7_JHtMsPzA4fXpXG-ex3blInftS5mQtAO-yNc_Rps", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C560BAQHyOt_wiEzzpw/company-logo_100_100/company-logo_100_100/0/1631337789085?e=**********&v=beta&t=KQ8KTXyY8H2DBGzTuu0a30waqDN5GyeEUfJpQoqgzCw", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C560BAQHyOt_wiEzzpw/company-logo_400_400/company-logo_400_400/0/1631337789085?e=**********&v=beta&t=gZ7C7rIFPcCun4bivWjTHxw4O859O5F_lDCDB9nRCeM", "expires_at": **********000}]}}, {"title": "US Hr and Immigration", "description": "H1B sponsorship ", "location": "United States · Remote", "date": {"start": "Aug 2024", "end": "Oct 2024"}, "employment_type": "Full-time", "company": {"name": "Stark Pro IT Inc.", "url": "https://www.linkedin.com/search/results/all/?keywords=Stark+Pro+IT+Inc%2E"}}, {"title": "Bench sales Executive", "date": {"start": "Jan 2024", "end": "Jul 2024"}, "employment_type": "Full-time", "company": {"id": "79760947", "name": "Excelon Solutions", "url": "https://www.linkedin.com/company/79760947/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQExPeE2SiYD-g/company-logo_200_200/company-logo_200_200/0/1630642485041?e=**********&v=beta&t=uU5khsJ4-BzdRTzV6Qt7-4HkZbL9qScQCFWuRs4r1OY", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQExPeE2SiYD-g/company-logo_100_100/company-logo_100_100/0/1630642485041?e=**********&v=beta&t=EA4odRNIcqVw2jgFviCrNMhBP-jBgQQkvmmWGJasvjY", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQExPeE2SiYD-g/company-logo_400_400/company-logo_400_400/0/1630642485041?e=**********&v=beta&t=x5mDd4oIJ5n4m3cJGabXfbz3op59sokeghYD1Ua0Ri4", "expires_at": **********000}]}}, {"title": "Bench sales recruiter", "location": "Charlotte, North Carolina, United States · On-site", "date": {"start": "Jul 2023", "end": "Jan 2024"}, "employment_type": "Full-time", "company": {"id": "90558081", "name": "Datics Inc", "url": "https://www.linkedin.com/company/90558081/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C560BAQH8ydBVxTJYzw/company-logo_200_200/company-logo_200_200/0/1677203002319?e=**********&v=beta&t=yWicsAy7r61AqcRXcN-qLPb4ezMTbsh8hQKeFqIKzGM", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C560BAQH8ydBVxTJYzw/company-logo_100_100/company-logo_100_100/0/1677203002319?e=**********&v=beta&t=kbjXpzGIaejYd48-kKIilSRhMHR8F_MukbsU2q6_mkc", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C560BAQH8ydBVxTJYzw/company-logo_400_400/company-logo_400_400/0/1677203002319?e=**********&v=beta&t=vuLOT8KcUlMkE5eOsc7Uyx1iFBraiElkpllwRiDNFKI", "expires_at": **********000}]}}, {"title": "Bench Sales Recruiter", "location": "Iselin, New Jersey, United States · On-site", "date": {"start": "May 2022", "end": "Jan 2024"}, "employment_type": "Full-time", "company": {"id": "11539655", "name": "3B Staffing LLC", "url": "https://www.linkedin.com/company/11539655/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGdw1rlZxCsdg/company-logo_200_200/company-logo_200_200/0/1631369207190?e=**********&v=beta&t=VxZzNXt-J3l6k4FQzusa1YoHybDatYsSi4yl2Pt__1o", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGdw1rlZxCsdg/company-logo_100_100/company-logo_100_100/0/1631369207190?e=**********&v=beta&t=i7N3F6PfjLxrUDg-cmFainUNwUKKE3dP8T7C8B0K20M", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGdw1rlZxCsdg/company-logo_400_400/company-logo_400_400/0/1631369207190?e=**********&v=beta&t=IG4zbV6jvVTpcgKTLd7MW9FEZlCaFme1_TnjLvRAPcQ", "expires_at": **********000}]}}], "skills": [{"skill": "Job Postings"}, {"skill": "Recruiting"}, {"skill": "Microsoft Office"}, {"skill": "Microsoft Excel"}, {"skill": "Negotiation"}, {"skill": "Marketing"}, {"skill": "Sales"}], "certifications": [], "publications": [], "educations": [], "honors": [], "volunteers": []}, {"id": "1005380973", "urn": "ACoAADvs5W0BZdo0kPEDCcSM7rEkI5vMuWPUTEA", "public_identifier": "yrvallela", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "M", "full_name": "Neha M", "headline": "Lead US IT Bench sales recruiter", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1654179346032, "created_date": "2022-06-02T14:15:46.032Z", "location": {"country": "India", "country_code": "IN", "city": "Hyderabad, Telangana, India", "postal_code": null}, "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Sr. bench sales recruiter", "date": {"start": "Jul 2023", "end": "Present"}, "employment_type": "Full-time", "company": {"id": "14732168", "name": "Artifint Technologies Private Limited", "url": "https://www.linkedin.com/company/14732168/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C510BAQFZIrKrjKom5w/company-logo_200_200/company-logo_200_200/0/1630576398692?e=**********&v=beta&t=fR-MHGWCQDvblqABdL_SHM7gF73zNDKsygiWOirztqc", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C510BAQFZIrKrjKom5w/company-logo_100_100/company-logo_100_100/0/1630576398692?e=**********&v=beta&t=FDk6WZmRDLfsm61wyTPGu3BCyO7wDG9bzDgLlNDI3rQ", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C510BAQFZIrKrjKom5w/company-logo_400_400/company-logo_400_400/0/1630576398692?e=**********&v=beta&t=jRjplWqbwTkWxKwMMVz-j0jxZf3Vqw5YWAC9FrajvyU", "expires_at": **********000}]}}], "skills": [], "certifications": [], "publications": [], "educations": [], "honors": [], "volunteers": []}, {"id": "*********", "urn": "ACoAACmcxi4BF4h06UG79jyHsPzxjNpdUx_X7bA", "public_identifier": "naveen-thipparapu-3a6980175", "first_name": "Na<PERSON><PERSON>", "last_name": "Thipparapu", "full_name": "<PERSON><PERSON><PERSON>", "headline": "Highdata Software Corp", "is_premium": true, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1542551174448, "created_date": "2018-11-18T14:26:14.448Z", "location": {"country": "United States", "country_code": "US", "city": "Salem, New Hampshire, United States", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C5603AQHRHMVOVJXhsg/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/1565827720407?e=**********&v=beta&t=vC2ejKSz3XgGFMWnvPCJfjzI_VH3j2TqA8CM3q93Jwc", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C5603AQHRHMVOVJXhsg/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1565827720383?e=**********&v=beta&t=Bn73XxMl0Yw824L3y1_a0Zec0C7pfoolElkpFazGNfw", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C5603AQHRHMVOVJXhsg/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1565827720436?e=**********&v=beta&t=Z1NVxM7BWkev6gWlVgAskVqw6gOrO2ib23i92ETmwps", "expires_at": **********000}, {"width": 592, "height": 592, "url": "https://media.licdn.com/dms/image/v2/C5603AQHRHMVOVJXhsg/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1565827720468?e=**********&v=beta&t=jyseuwdF4xupoK2FqGPiXFFoSCbg-0cWbzwki0tc_00", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Sr. Bench Sales Recruiter", "location": "New Hampshire, United States · On-site", "date": {"start": "Jan 2023", "end": "Present"}, "employment_type": "Full-time", "company": {"id": "90842", "name": "Highdata Software Corp", "url": "https://www.linkedin.com/company/90842/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQEy_rSAiKgEXQ/company-logo_200_200/company-logo_200_200/0/1681396425829/highdata_software_corp_logo?e=**********&v=beta&t=zkgDCnRr4IXVGbNgeieq1svNLQJnOuP7qLhmG1wMROs", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQEy_rSAiKgEXQ/company-logo_100_100/company-logo_100_100/0/1681396425829/highdata_software_corp_logo?e=**********&v=beta&t=uWtriWKOKDq9RemSZM8jOlkMYvS2MZt5PIBhX1ueJjM", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQEy_rSAiKgEXQ/company-logo_400_400/company-logo_400_400/0/1681396425829/highdata_software_corp_logo?e=**********&v=beta&t=LqO8YQVbXz34Y3JE3lC4H6AD-BM-QvsZ4PNsfzvBMEc", "expires_at": **********000}]}}, {"title": "Sr Sales recruiter at Realsoft technologies llc", "description": "Realsoft Technologies llc :Our Mission is to satisfy our customers, to work closely with our consultants by giving them valid resources and solving the problems. To constantly deliver the results based on integrity and team work.\n\nHave enough experience in Full-lifecycle of recruitment including sourcing, screening, interviewing, negotiating packages, recommending candidates for employment, managing clients, Staffing and building relationships with Technical Professionals and Vendors.\n•\tHave experience in Recruiting H1B Consultants, Green card Holders, EAD, TN, OPT and US Citizens for Direct Client Requirements & Tier 1 Vendor Requirements.\n•\tAdept in the use of internet-based resume sourcing including <PERSON><PERSON>, Monster (US), Career Builder, LinkedIn, TechFetch, The Ladders. \n•\tExpert in Complete Recruitment Life Cycle working experience from requirement procurement, sourcing, screening, contract signing to closure.\n•\tExpert in US Tax terms like Corp-to-Corp, 1099, W2 and other Visas like EAD, EAD-L2, OPT, CPT and TN \n•\tInvolved in Vendor contract signing and client Relationship management. \n•\tGood experience in working of Full time and contract, Contract to hire job opportunities.\n•\tCommanding sourcing capabilities, screening, interviewing , negotiations & follow-ups\n•\tExplored various job portals like DICE, Career Builder, and MONSTER, TechFetch, The Ladders etc…\n•\tExpert in preparing, formatting resumes and on different MIS.\n\nOur Team work at our various direct and indirect client locations, always satisfied the needs and requirements of our customers, we work effectively as a unit and drive our organizational indigence.\nI have candidates for Java. MSBI, .Net, Tableau, DB2 DBA , Sharepoint, Salesforce, QA Tester's etc .. please <NAME_EMAIL> ", "location": "Herndon , VA", "date": {"start": "May 2019", "end": "Dec 2022"}, "company": {"id": "487910", "name": "Realsoft Technologies, LLC", "url": "https://www.linkedin.com/company/487910/"}}, {"title": "Process Executive", "location": "Hyderabad, Telangana, India", "date": {"start": "Jan 2018", "end": "Feb 2019"}, "employment_type": "Full-time", "company": {"id": "164151", "name": "HDFC Bank", "url": "https://www.linkedin.com/company/164151/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C510BAQGqZH7vVbVzWw/company-logo_200_200/company-logo_200_200/0/*************/hdfc_bank_logo?e=**********&v=beta&t=TaffW3wcHUXk8qqaNu2xVspI7AzN9zZYC5zcW5_CkUA", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C510BAQGqZH7vVbVzWw/company-logo_100_100/company-logo_100_100/0/*************/hdfc_bank_logo?e=**********&v=beta&t=7nd-pNlZlJCA_UTExZbW7o4WBlGuZ7DSatrgqe7u_l0", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C510BAQGqZH7vVbVzWw/company-logo_400_400/company-logo_400_400/0/*************/hdfc_bank_logo?e=**********&v=beta&t=xZAkc9okxhKX0EB4PIhkwuHIYk-TQDsia0FfdzL3J_I", "expires_at": **********000}]}}], "skills": [], "certifications": [], "publications": [], "educations": [{"school": "<PERSON><PERSON><PERSON>", "date": {"start": "2012", "end": "2016"}, "degree": "Bachelor of Technology - BTech, Electrical and Electronics Engineering"}], "honors": [], "volunteers": []}, {"id": "*********", "urn": "ACoAACj5GTAB8C8L6mFW-5N9IVjFQXJbXb0_FnM", "public_identifier": "lakshmi-prasanna-*********", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "full_name": "<PERSON>", "headline": "US IT Recruiter/ Bench sales", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": *************, "created_date": "2018-10-01T12:33:20.993Z", "location": {"country": "India", "country_code": "IN", "city": "Andhra Pradesh, India", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E03AQFO1l8nry6CNw/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/1562061382428?e=**********&v=beta&t=Tk4uf7-oyo4OT1Rd35MqjQC3kJQkm9oUxLd4eqkjhkM", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E03AQFO1l8nry6CNw/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1562061382411?e=**********&v=beta&t=5nkCwUiz7YqUbf7Vl9SUxCDFF0dsJdZlKE3SRR-Lz10", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E03AQFO1l8nry6CNw/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1562061382538?e=**********&v=beta&t=erBHNQgBXYlBNmlnP4IZIQzM2fBVSExxMrVXb82Rt2M", "expires_at": **********000}, {"width": 600, "height": 600, "url": "https://media.licdn.com/dms/image/v2/C4E03AQFO1l8nry6CNw/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1562061382385?e=**********&v=beta&t=_XpzGfDoF2pwnw29NfHeRWMaiaaaqoi3lQTR42MZUug", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Us IT recruiter ", "location": "Hyderabad, Telangana, India · On-site", "date": {"start": "Jan 2024", "end": "Present"}, "employment_type": "Full-time", "company": {"id": "14732168", "name": "Artifint Technologies Private Limited", "url": "https://www.linkedin.com/company/14732168/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C510BAQFZIrKrjKom5w/company-logo_200_200/company-logo_200_200/0/1630576398692?e=**********&v=beta&t=fR-MHGWCQDvblqABdL_SHM7gF73zNDKsygiWOirztqc", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C510BAQFZIrKrjKom5w/company-logo_100_100/company-logo_100_100/0/1630576398692?e=**********&v=beta&t=FDk6WZmRDLfsm61wyTPGu3BCyO7wDG9bzDgLlNDI3rQ", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C510BAQFZIrKrjKom5w/company-logo_400_400/company-logo_400_400/0/1630576398692?e=**********&v=beta&t=jRjplWqbwTkWxKwMMVz-j0jxZf3Vqw5YWAC9FrajvyU", "expires_at": **********000}]}}, {"title": "Us IT bench sales recruiter ", "location": "On-site", "date": {"start": "Sep 2022", "end": "Present"}, "employment_type": "Full-time", "company": {"id": "89557079", "name": "Fruges IT Services India Private Ltd", "url": "https://www.linkedin.com/company/89557079/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQEf68bE9kIrew/company-logo_200_200/company-logo_200_200/0/1664432757013?e=**********&v=beta&t=mD994UfCIbRNrKNPdXxjzrWYKSfRiCXU8anSSeaq9mk", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQEf68bE9kIrew/company-logo_100_100/company-logo_100_100/0/1664432757013?e=**********&v=beta&t=Msu3wtFFtWX43_GgwGPkyi6AmnPEPidfbiGGscy6YRs", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQEf68bE9kIrew/company-logo_400_400/company-logo_400_400/0/1664432757013?e=**********&v=beta&t=J3iOyeK-AonCh9Y43YiQGw5rPEwkcInAWanzDtms4hE", "expires_at": **********000}]}}], "skills": [], "certifications": [], "publications": [], "educations": [{"school": "Prakasam Engineering College, O.V.Road, Kandukur,PIN- 523105(CC-F9)", "date": {"start": "2015", "end": "2019"}, "degree": "B.tech,  Electronics and Communications Engineering"}], "honors": [], "volunteers": []}]