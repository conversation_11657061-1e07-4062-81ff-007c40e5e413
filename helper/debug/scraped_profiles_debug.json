[{"id": "620762667", "urn": "ACoAACUAFisBpdPv6Ccpa4xz1AfpkimfkDsnTEY", "public_identifier": "riddhiman-deka", "first_name": "<PERSON><PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON>", "full_name": "<PERSON><PERSON><PERSON><PERSON>", "headline": "Business Analyst | SQL | Python | Tableau | Power BI | Data Analytics | MS Business Analytics @ Syracuse ‘25 | Ex-Senior Executive, Reliance Retail", "is_premium": true, "is_open_to_work": true, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": 18, "month": 3, "year": null}, "pronoun": null, "created": 1510904389007, "created_date": "2017-11-17T07:39:49.007Z", "location": {"country": "United States", "country_code": "US", "city": "Syracuse, New York, United States", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D4D03AQEVfnS3oX8cug/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/1676905954558?e=**********&v=beta&t=7yaOFrhONQn5nncmbC5gxROE5qUGalBMFfxnJoHrLG4", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D03AQEVfnS3oX8cug/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1676905954558?e=**********&v=beta&t=Q65LhBvA362YBumeSclmzqFSvpKc3yxTPCHFjWJooyU", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D4D03AQEVfnS3oX8cug/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1676905954558?e=**********&v=beta&t=mU65PQyGCaD1jh6CytX8R6NDMQZFSnz35w2QhAo_1XY", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D4D03AQEVfnS3oX8cug/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1676905954558?e=**********&v=beta&t=KKyn9OUAWRsOkw4eFdM8DybdeP9Y98gzTge0jsDr9_A", "expires_at": **********000}], "cover": [{"width": 800, "height": 199, "url": "https://media.licdn.com/dms/image/v2/D5616AQHIXHK8lyATEw/profile-displaybackgroundimage-shrink_200_800/profile-displaybackgroundimage-shrink_200_800/0/1724876038111?e=**********&v=beta&t=VGEaRJn-D7PONmrLgmS11tucQbfFVwY1A1T_AhcgqfA", "expires_at": **********000}, {"width": 979, "height": 244, "url": "https://media.licdn.com/dms/image/v2/D5616AQHIXHK8lyATEw/profile-displaybackgroundimage-shrink_350_1400/profile-displaybackgroundimage-shrink_350_1400/0/1724876038111?e=**********&v=beta&t=NncLvNXWpfe2P-M33ncSSvuKOBFMw53VHCDqs_Pr5QQ", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Student Staff", "description": "Skills: Data Storytelling · Google Analytics · Data Collection", "location": "Syracuse, New York, United States", "date": {"start": "Aug 2023", "end": "Present"}, "employment_type": "Part-time", "company": {"id": "166228", "name": "Syracuse University", "url": "https://www.linkedin.com/company/166228/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C560BAQFAyZ5pXkHL9Q/company-logo_200_200/company-logo_200_200/0/1630671468743/syracuse_university_logo?e=**********&v=beta&t=qcY9lFLA7CT0sLxM_8yQOdWDm2k7s-Xb2rZbPQQv2Jo", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C560BAQFAyZ5pXkHL9Q/company-logo_100_100/company-logo_100_100/0/1630671468743/syracuse_university_logo?e=**********&v=beta&t=_ZxbR1EC-p2Lgrs5Nw2hZH6YC0pjbWcliSSmUxddXH0", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C560BAQFAyZ5pXkHL9Q/company-logo_400_400/company-logo_400_400/0/1630671468743/syracuse_university_logo?e=**********&v=beta&t=UZjscfhqwgjKUT2VTqxcmPCi_sO6qGE0fm_m1Mt6_c4", "expires_at": **********000}]}}, {"title": "Supply Chain Business Analyst", "description": "●\tSupervised warehouse inbound and outbound operations using SAP and analyzed data to optimize warehouse space, leading to an 8% reduction in inventory holding cost.\n●\tOptimized inventory management in Excel by leveraging historical and real-time tracking data for different storage locations, identifying improvement opportunities within the supply chain, resulting in increased efficiency by 22% and an annual savings of $80K.\n●\tCreated dynamic dashboards in Tableau to track and measure KPIs for transportation and inventory management, leading to a better understanding of the company’s performance across departments, enhancing strategic alignment, and improving collaboration between cross-functional teams.\n●\tReverse-engineered existing SQL queries and optimized the SQL to extract accurate data with better efficiency, reducing the query execution time by 25%, and resulting in returns being processed 20% faster.\n●\tDesigned and maintained dashboards that helped summarize the delivery performance of 6 high-risk inbound suppliers and communicated and mitigated top risks through proactive intervention, resulting in past dues reduction by 30% \n●\tCollaborated with cross-functional teams to gather requirements and developed optimized SQL queries for inventory and warehouse management reports and scheduled automated report deliveries through Tableau, reducing manual time invested by 80%.", "location": "India · On-site", "date": {"start": "Jun 2021", "end": "Jan 2023"}, "employment_type": "Full-time", "skills": ["Organization Skills", "Quality Management", "Business Requirements", "SAP ERP", "5S", "Data Strategies", "Reverse Logistics", "Data Storytelling", "Data Visualization", "Presentation Skills", "Analytical Skills", "SAP CRM", "SQL", "Problem Solving", "Customer Experience Management", "Microsoft Excel", "Presentations", "Warehouse Management", "Business Analytics", "Inventory Management", "Operations Management", "Communication", "Writing", "Interpersonal Skills", "Negotiation", "Data Analysis"], "company": {"id": "2375", "name": "Reliance Retail", "url": "https://www.linkedin.com/company/2375/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQHM_iN30Rgv3w/company-logo_200_200/company-logo_200_200/0/1631307312115?e=**********&v=beta&t=jHVN-ikepvDiiUwlKFV9p-PVvkOa4I7pIl_y8Q1_c4U", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQHM_iN30Rgv3w/company-logo_100_100/company-logo_100_100/0/1631307312115?e=**********&v=beta&t=2B9WaWSeBaYMPnJWszKyZHg2oDwDdL_1GgPvbSnONBw", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQHM_iN30Rgv3w/company-logo_400_400/company-logo_400_400/0/1631307312115?e=**********&v=beta&t=CqYrgI7gXUK6Ind0_xE1t8H38DxlmZ53bx9bYUCd3b8", "expires_at": **********000}]}}, {"title": "Intern", "description": "- Created a process flow for assembly station which reduced downtime by improving productivity by 30%\n- Assisted in streamlining the material movement process and eliminated non-value added work\n- Analyze historical customer demand and created a forecasting model in excel using linear regression and moving average\n- Published Tableau reports on Tableau cloud server and scheduled refreshes on the server", "location": "Guwahati, Assam, India · On-site", "date": {"start": "May 2020", "end": "Jun 2020"}, "skills": ["Presentation Skills", "Sales Leadership", "Presentations", "Communication", "Writing", "Interpersonal Skills", "Negotiation"], "company": {"id": "93188903", "name": "M/S POWER MAKER UNIT-II", "url": "https://www.linkedin.com/company/93188903/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQFSyu1F13ITQQ/company-logo_200_200/company-logo_200_200/0/1682054758700/m_s_power_maker_unit_ii_logo?e=**********&v=beta&t=uQMWjPJcVisjxHpSOk_K_eB_WAW2S0SX5xydpPTh7iI", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQFSyu1F13ITQQ/company-logo_100_100/company-logo_100_100/0/1682054758700/m_s_power_maker_unit_ii_logo?e=**********&v=beta&t=UW1qG3Ya3UWGLwWJp2MHb_AMQEd0pHXQJ-4letw4cCw", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQFSyu1F13ITQQ/company-logo_400_400/company-logo_400_400/0/1682054758700/m_s_power_maker_unit_ii_logo?e=**********&v=beta&t=Rx268Vb0I36lL7bmYZyQA5cX35x8C7-zaoXsJ__So0E", "expires_at": **********000}]}}, {"title": "Intern", "description": "- Aided in analysis of sales and production metrics KPIs, generated reports that highlighted trends and opportunities for business development optimization\n- Performed detailed design analysis and maintained reports on ongoing projects of the department\n- Worked with design team to create design for excavators, produced detailed drawings in AutoCAD and Solidworks\n- Demonstrated a proactive and results-oriented approach to learning, taking ownership of tasks and seeking opportunities to contribute value to the business development team", "location": "Noida, Uttar Pradesh, India", "date": {"start": "Apr 2020", "end": "May 2020"}, "skills": ["Pivot Tables", "Presentation Skills", "Customer Experience Management", "Microsoft Excel", "Presentations", "AutoCAD", "Communication", "Writing", "Interpersonal Skills"], "company": {"id": "30228566", "name": "Kobelco Industrial Machinery India Private Limited", "url": "https://www.linkedin.com/company/30228566/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C510BAQGUyHIiy7NG-w/company-logo_200_200/company-logo_200_200/0/1630631918336/kobelco_industrial_machinery_india_private_limited_logo?e=**********&v=beta&t=ODhC0iF1OnNfz16IgcBdkbMddeAQaF5Gt-iJd1Uzd7Q", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C510BAQGUyHIiy7NG-w/company-logo_100_100/company-logo_100_100/0/1630631918336/kobelco_industrial_machinery_india_private_limited_logo?e=**********&v=beta&t=mq_L_dGo_HlnDCyO5g80B35v89LqibAfHuIWB8BR23I", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C510BAQGUyHIiy7NG-w/company-logo_400_400/company-logo_400_400/0/1630631918336/kobelco_industrial_machinery_india_private_limited_logo?e=**********&v=beta&t=WXRMoYwZ-A8aqPzAfmzXS8gXuQdEPmhQHwauCm5Vjo0", "expires_at": **********000}]}}], "skills": [{"skill": "Amazon Web Services (AWS)"}, {"skill": "Data Storytelling"}, {"skill": "Google Analytics"}, {"skill": "Data Collection"}, {"skill": "Business Case Preparation"}, {"skill": "Data Systems"}, {"skill": "Target Audience"}, {"skill": "Business Documentation"}, {"skill": "Research Skills"}, {"skill": "Visualization"}, {"skill": "Attention to Detail"}, {"skill": "Negotiation"}, {"skill": "Teamwork"}, {"skill": "Databases"}, {"skill": "Communication"}, {"skill": "Data Strategies"}, {"skill": "Sales Leadership"}, {"skill": "Competitive Analysis"}, {"skill": "Microsoft Power BI"}, {"skill": "Excel Pivot"}], "certifications": [{"title": "Data Analyst Certification", "authority": "OneRoadmap", "credential_url": "https://oneroadmap.io/skills/da/certificate/CERT-3D88AC8F", "issued_at": "Apr 2025"}, {"title": "Content Marketing", "authority": "HubSpot Academy", "credential_url": "https://app.hubspot.com/academy/achievements/lsgf8zn3/en/1/riddhiman-deka/content-marketing", "issued_at": "Dec 2024 · Expires Jan 2027"}, {"title": "Welcome Ambassadors International Orientation Leader", "authority": "Syracuse University", "credential_url": "https://badges.syr.edu/4d84f882-15cc-4926-b853-45f423b96605", "issued_at": "Aug 2024"}, {"title": "Operations Management Foundations", "authority": "LinkedIn", "credential_url": "https://www.linkedin.com/learning/certificates/256ce0ca8bd800abe2316881eee0a9c7ca233496676ceb2bc6b307160dabe5a1?trk=backfilled_certificate", "issued_at": "Sep 2021"}, {"title": "Quality Management Foundations", "authority": "LinkedIn", "credential_url": "https://www.linkedin.com/learning/certificates/097c5e704d61726a37ff23b9f6f474797b3e3e1e191d639dde226c38a667efe6?trk=backfilled_certificate", "issued_at": "Sep 2021"}, {"title": "Supply Chain Foundations", "authority": "LinkedIn", "credential_url": "https://www.linkedin.com/learning/certificates/304d54bc2062b5e0afa52b811d6dc7a80420bade9b3f70eff1d0ab1f911cc2f5?trk=backfilled_certificate", "issued_at": "Sep 2021"}], "publications": [], "educations": [{"school": "Syracuse University - Martin <PERSON> School of Management", "date": {"start": "Aug 2023", "end": "May 2025"}, "degree": "Master of Science - MS, Business Analytics", "description": "Skills: Case Studies · Research Skills · Data Systems · Data Science · Business Requirements · Databases · SQL · Writing · Presentation Skills · Business Documentation · Data Collection · Business Case Preparation · Business Analysis · Interpersonal Skills · Organization Skills · Visualization · Data Analytics · R (Programming Language) · Google Analytics · Teamwork · Business Analytics · Attention to Detail · Problem Solving · Microsoft Excel · Presentations · Data Storytelling · Data Visualization · Analytical Skills · Amazon Web Services (AWS)", "media": [{"description": null, "thumbnail": [{"width": 1487, "height": 1925, "url": "https://media.licdn.com/dms/image/v2/D4E2DAQEHhwCD4hj5UA/profile-treasury-document-cover-images_1920/B4EZXFeN8fHMBI-/0/1742774789426?e=**********&v=beta&t=pZxBlpJaPvmatryHMCUBCkRBF6TaCoFInFKIJxNzXCE", "expires_at": **********000}, {"width": 374, "height": 484, "url": "https://media.licdn.com/dms/image/v2/D4E2DAQEHhwCD4hj5UA/profile-treasury-document-cover-images_480/B4EZXFeN8fHMA0-/0/1742774788879?e=**********&v=beta&t=PbJbtE-AgqzZ3IKaRunlwxLkuWEwO86krrhqybzNprU", "expires_at": **********000}, {"width": 620, "height": 802, "url": "https://media.licdn.com/dms/image/v2/D4E2DAQEHhwCD4hj5UA/profile-treasury-document-cover-images_800/B4EZXFeN8fHMBE-/0/1742774789142?e=**********&v=beta&t=o6hVyfDThvfwFTv5TWTuiclH5IpJwHr96to9kb8lCd4", "expires_at": **********000}, {"width": 994, "height": 1287, "url": "https://media.licdn.com/dms/image/v2/D4E2DAQEHhwCD4hj5UA/profile-treasury-document-cover-images_1280/B4EZXFeN8fHMA4-/0/1742774789253?e=**********&v=beta&t=F_O4Na3d4_QlBwi9f-UJyTu5BA-pd3ely0rDLJG1jy0", "expires_at": **********000}], "title": "<PERSON><PERSON><PERSON><PERSON>ka Resume_03232025.pdf"}, {"description": null, "thumbnail": [{"width": 994, "height": 1287, "url": "https://media.licdn.com/dms/image/v2/D4E2DAQFUOyGvr-f4lQ/profile-treasury-document-cover-images_1280/B4EZURkGTFG0A0-/0/1739756431869?e=**********&v=beta&t=JSLk660YYNdVEvuHYfmsHvFcVbBzSjmCskie88I-Dms", "expires_at": **********000}, {"width": 620, "height": 802, "url": "https://media.licdn.com/dms/image/v2/D4E2DAQFUOyGvr-f4lQ/profile-treasury-document-cover-images_800/B4EZURkGTFG0BA-/0/1739756431737?e=**********&v=beta&t=cQ0Z054QFclkdTt5t6z9JkoNUNojRRw1A0RPUsd81P8", "expires_at": **********000}, {"width": 374, "height": 484, "url": "https://media.licdn.com/dms/image/v2/D4E2DAQFUOyGvr-f4lQ/profile-treasury-document-cover-images_480/B4EZURkGTFG0Aw-/0/1739756431658?e=**********&v=beta&t=PsIAqtz0ZpG-bNqCT_HCgLkfHRYSGuM5_0Hl_t6dc3M", "expires_at": **********000}, {"width": 1487, "height": 1925, "url": "https://media.licdn.com/dms/image/v2/D4E2DAQFUOyGvr-f4lQ/profile-treasury-document-cover-images_1920/B4EZURkGTFG0BE-/0/1739756432114?e=**********&v=beta&t=yBmjWfL6mChl1jrZeIPeQhQbe66EsATypqYbGJtv2Po", "expires_at": **********000}], "title": "<PERSON><PERSON><PERSON><PERSON> Resume_021625 .pdf"}], "skills": ["Case Studies", "Research Skills", "Data Systems", "Data Science", "Business Requirements", "Databases", "SQL", "Writing", "Presentation Skills", "Business Documentation", "Data Collection", "Business Case Preparation", "Business Analysis", "Interpersonal Skills", "Organization Skills", "Visualization", "Data Analytics", "R (Programming Language)", "Google Analytics", "Teamwork", "Business Analytics", "Attention to Detail", "Problem Solving", "Microsoft Excel", "Presentations", "Data Storytelling", "Data Visualization", "Analytical Skills", "Amazon Web Services (AWS)"]}, {"school": "Amity University", "date": {"start": "Sep 2017", "end": "Jun 2021"}, "degree": "Bachelor of Engineering - BE, Mechanical Engineering", "description": "Skills: Case Studies · Customer Experience Management · Writing · Presentation Skills · Business Case Preparation · Interpersonal Skills · Teamwork · Problem Solving · Presentations · Target Audience", "skills": ["Case Studies", "Customer Experience Management", "Writing", "Presentation Skills", "Business Case Preparation", "Interpersonal Skills", "Teamwork", "Problem Solving", "Presentations", "Target Audience"]}, {"school": "Syracuse University - Martin <PERSON> School of Management", "date": {"start": "Aug 2023"}, "degree": "Master's degree, Business, Management, Marketing, and Related Support Services", "grade": "3.4", "skills": ["R (Programming Language)", "Excel Pivot", "<PERSON><PERSON>", "Microsoft Power BI", "Microsoft Excel", "Competitive Analysis"]}], "honors": [], "volunteers": []}, {"id": "866649067", "urn": "ACoAADOoA-sBJGcb0n2N0xV1cKFSZMhoVbtEiVU", "public_identifier": "radhika-gaikwad-0372a5202", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Gaikwad", "full_name": "<PERSON><PERSON><PERSON>", "headline": "Cyber Security enthusiast | IT Analyst | Bachelor of Technology in Computer Engineering | Hons in Cyber security", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1608625684075, "created_date": "2020-12-22T08:28:04.075Z", "location": {"country": "India", "country_code": "IN", "city": "Pune, Maharashtra, India", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D4D03AQGsEPsNvLZ04Q/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/1720099994507?e=**********&v=beta&t=OHpe7Ka9YLuEi1zBQ7No_vRTLDJ08osr3_WCU9OL-n0", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D03AQGsEPsNvLZ04Q/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1720099994507?e=**********&v=beta&t=tbsa4v9I_Rzu9uZqxfiPk0a5uJ2ZYXK18kRoS1cMWwM", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D4D03AQGsEPsNvLZ04Q/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1720099994507?e=**********&v=beta&t=-UMdZm1SV8qDLTlk1lXN8EJBCCCWYmc-LesGWuPO75g", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D4D03AQGsEPsNvLZ04Q/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1720099994549?e=**********&v=beta&t=J232gvfl9Tj5Wiz9h_lE41HT2jxf0hwvV2KxdCoAcyA", "expires_at": **********000}], "cover": [{"width": 800, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D16AQH-R-wb57o08g/profile-displaybackgroundimage-shrink_200_800/profile-displaybackgroundimage-shrink_200_800/0/1720110849804?e=**********&v=beta&t=YWT422VnG-VCGqLz53UbsFUMvrf4WQ2Ls0_yebzTRoM", "expires_at": **********000}, {"width": 1080, "height": 270, "url": "https://media.licdn.com/dms/image/v2/D4D16AQH-R-wb57o08g/profile-displaybackgroundimage-shrink_350_1400/profile-displaybackgroundimage-shrink_350_1400/0/1720110849804?e=**********&v=beta&t=ZGVur3KYyZ2ySiPU9Vz6L8bE-vsYgDKYlEO5qz0CYdk", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "IT Analyst", "location": "Pune, Maharashtra, India", "date": {"start": "Jul 2024", "end": "Present"}, "employment_type": "Full-time", "company": {"id": "2604", "name": "Tetra Pak", "url": "https://www.linkedin.com/company/2604/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQEH4K23HGR8Eg/company-logo_200_200/company-logo_200_200/0/1630643325145/tetra_pak_logo?e=**********&v=beta&t=mZtuFO7gG8FXfhNDGD0sFyW9Rh_oA1Uh_C8TjBJkYq4", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQEH4K23HGR8Eg/company-logo_100_100/company-logo_100_100/0/1630643325145/tetra_pak_logo?e=**********&v=beta&t=dXzP5bC2ABYi8BbEp-dx2iQqWNp9fUozfZm8mqWhkLU", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQEH4K23HGR8Eg/company-logo_400_400/company-logo_400_400/0/1630643325145/tetra_pak_logo?e=**********&v=beta&t=nReDjRvfr1u6tnO7Tsg-M4W197f-yrly-g0n8YEhPHc", "expires_at": **********000}]}}, {"title": "Student Volunteer", "description": "UI/ UX Designer", "location": "Pune, Maharashtra, India", "date": {"start": "Jun 2023", "end": "Sep 2023"}, "employment_type": "Full-time", "company": {"id": "98052494", "name": "Flutter Pune", "url": "https://www.linkedin.com/company/98052494/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQFJ1n0m2UsYAQ/company-logo_200_200/company-logo_200_200/0/1694625536371/flutter_pune_logo?e=**********&v=beta&t=br6Mi54ZfIStgJt9HwZUWPgNC9y4mXqCsvadNllf7fw", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQFJ1n0m2UsYAQ/company-logo_100_100/company-logo_100_100/0/1694625536371/flutter_pune_logo?e=**********&v=beta&t=E022lWuF2Ss4fIXBhEY2I6EfM6lY89t7jEcfykPnKPA", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQFJ1n0m2UsYAQ/company-logo_400_400/company-logo_400_400/0/1694625536371/flutter_pune_logo?e=**********&v=beta&t=ZlgEMYeqzRn_3mv-82nYHdBUYMaM0Th7-j5ngjMMT-Q", "expires_at": **********000}]}}, {"title": "Social Media Head", "description": "Skills: Problem Solving · Marketing · Passionate about Work · Media Management", "location": "Pune/Pimpri-Chinchwad Area", "date": {"start": "Aug 2022", "end": "Aug 2023"}, "employment_type": "Full-time", "company": {"id": "67926186", "name": "GDSC PCCoE", "url": "https://www.linkedin.com/company/67926186/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQFMk_tWh1SY8g/company-logo_200_200/company-logo_200_200/0/1729266019668/dsc_pccoe_logo?e=**********&v=beta&t=g86w2y9tdPJVUUMEnM2unvTKfsUlvNVFCfZgJ3NG5PM", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQFMk_tWh1SY8g/company-logo_100_100/company-logo_100_100/0/1729266019668/dsc_pccoe_logo?e=**********&v=beta&t=FiRlaxb9gJ8l6nEgwOAAAZJPobIAVL2tsXEE_bmudCc", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQFMk_tWh1SY8g/company-logo_400_400/company-logo_400_400/0/1729266019668/dsc_pccoe_logo?e=**********&v=beta&t=W_U_A-H4Rpehd0v5U8Xuo2GrBYpMxcBi2Q5IYjObjhk", "expires_at": **********000}]}}, {"title": "Social Media and External Affairs Head", "description": "https://instagram.com/pccoeacm?igshid=YmMyMTA2M2Y=", "date": {"start": "Aug 2022", "end": "Aug 2023"}, "skills": ["Brand Awareness", "Problem Solving", "Passionate about Work", "Social Media Management", "Media Management"], "company": {"id": "13366681", "name": "PCCOE ACM Student Chapter", "url": "https://www.linkedin.com/company/13366681/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFIWUNdoIMh7A/company-logo_200_200/company-logo_200_200/0/1630512409962?e=**********&v=beta&t=Y-GIkA8Teuy5KSdCjl7a2mwtpEbaM-3vZVfGwWb2RGA", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFIWUNdoIMh7A/company-logo_100_100/company-logo_100_100/0/1630512409962?e=**********&v=beta&t=M1cdoMLYgfCLUKC4xxhFU2GSgKpGGkPmAoZ5AXcnkUU", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFIWUNdoIMh7A/company-logo_400_400/company-logo_400_400/0/1630512409962?e=**********&v=beta&t=IJ1k61MeV11FkrASx4aa3NlCUxSwuwaBeqqEZlhzlNg", "expires_at": **********000}]}}, {"title": "Assisting weekly ThursdayTycoon", "description": "Weekly posts about Indian startups and discuss their different strategies", "date": {"start": "Dec 2021", "end": "Sep 2022"}, "skills": ["Passionate about Work", "Easily Adaptable", "Outreach Programs"], "company": {"id": "13366681", "name": "PCCOE ACM Student Chapter", "url": "https://www.linkedin.com/company/13366681/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFIWUNdoIMh7A/company-logo_200_200/company-logo_200_200/0/1630512409962?e=**********&v=beta&t=Y-GIkA8Teuy5KSdCjl7a2mwtpEbaM-3vZVfGwWb2RGA", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFIWUNdoIMh7A/company-logo_100_100/company-logo_100_100/0/1630512409962?e=**********&v=beta&t=M1cdoMLYgfCLUKC4xxhFU2GSgKpGGkPmAoZ5AXcnkUU", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFIWUNdoIMh7A/company-logo_400_400/company-logo_400_400/0/1630512409962?e=**********&v=beta&t=IJ1k61MeV11FkrASx4aa3NlCUxSwuwaBeqqEZlhzlNg", "expires_at": **********000}]}}, {"title": "Art circle cell representative ", "description": "Skills: Passionate about Work · Outreach Programs", "date": {"start": "Oct 2021", "end": "Sep 2022"}, "company": {"id": "13366681", "name": "PCCOE ACM Student Chapter", "url": "https://www.linkedin.com/company/13366681/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFIWUNdoIMh7A/company-logo_200_200/company-logo_200_200/0/1630512409962?e=**********&v=beta&t=Y-GIkA8Teuy5KSdCjl7a2mwtpEbaM-3vZVfGwWb2RGA", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFIWUNdoIMh7A/company-logo_100_100/company-logo_100_100/0/1630512409962?e=**********&v=beta&t=M1cdoMLYgfCLUKC4xxhFU2GSgKpGGkPmAoZ5AXcnkUU", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFIWUNdoIMh7A/company-logo_400_400/company-logo_400_400/0/1630512409962?e=**********&v=beta&t=IJ1k61MeV11FkrASx4aa3NlCUxSwuwaBeqqEZlhzlNg", "expires_at": **********000}]}}, {"title": "Campus Brand Influencer ", "description": "-Create and curate content related to college life, including but not limited to campus events, clubs, organizations, and local activities.\n-Collaborate with other team members to generate ideas and develop content that resonates with our audience.\n-Manage social media channels and engage with users to increase brand awareness and user engagement.\n-Continuously improve content quality and user engagement through testing and experimentation.", "location": "Pune, Maharashtra, India", "date": {"start": "May 2023", "end": "Jul 2023"}, "employment_type": "Internship", "company": {"id": "17960709", "name": "BEEP by EventBeep", "url": "https://www.linkedin.com/company/17960709/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQHb0mhg2hL8_w/company-logo_200_200/company-logo_200_200/0/1662547426634/eventbeep_entertainment_logo?e=**********&v=beta&t=IP9egk39IIWIe9L0MdKLAtJKT3aZXwcLyVsvdWhckCU", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQHb0mhg2hL8_w/company-logo_100_100/company-logo_100_100/0/1662547426634/eventbeep_entertainment_logo?e=**********&v=beta&t=i14IIiWO1bOr-3TyPV2ak8pg83X_FscfhaDkEmkJyUU", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQHb0mhg2hL8_w/company-logo_400_400/company-logo_400_400/0/1662547426634/eventbeep_entertainment_logo?e=**********&v=beta&t=oDEOJWEPNuf4BxClgymKxhISuKH7c6Bz-WS-lVBqLoE", "expires_at": **********000}]}}, {"title": "Student Intern", "description": "Skills: Machine Learning foundation · Cloud foundations · AI-ML", "location": "Remote", "date": {"start": "May 2023", "end": "Jul 2023"}, "employment_type": "Internship", "company": {"id": "64868089", "name": "AICTE", "url": "https://www.linkedin.com/company/64868089/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFSrJpj8UoamA/company-logo_200_200/company-logo_200_200/0/1659413613554/aicteindia_logo?e=**********&v=beta&t=DjRvPpqedz9CzvBCJyMqPe923ZkJ7zICo8qTAVe67AY", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFSrJpj8UoamA/company-logo_100_100/company-logo_100_100/0/1659413613554/aicteindia_logo?e=**********&v=beta&t=7qRjNKVia_n6qRzmcMLSxH_snF7uQXzyZOYmTpVHD8E", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQFSrJpj8UoamA/company-logo_400_400/company-logo_400_400/0/1659413613554/aicteindia_logo?e=**********&v=beta&t=cbVNCkPtl6YqEzuGhZYn1exPoj9VWY24cImHytEmCGM", "expires_at": **********000}]}}, {"title": "ACM Summer School (Cisco)", "description": "Cybersecurity\n-Employ new methods powered by social engineering and artificial intelligence (AI) to circumvent traditional data security controls.\n-Approaches to protecting infrastructure, including securing data and information, running risk analysis and mitigation, architecting cloud based security, and much more from the best experts of this domain.\n-Explore the roadmap to become an ethical hacker.\n-List of topics: Cryptography, Network Security, IoT Security, Light\u0002Weight encryption, Trust Schemes for IoT, Privacy protection in IoT, Quantum Cryptography, Post-Quantum Cryptography, Privacy preservation techniques, Blockchain Technology, Open-source intelligence, Risk analysis and mitigation, Cloud-based security, Career opportunities, challenges, and support", "location": "Chennai, Tamil Nadu, India", "date": {"start": "Jun 2023", "end": "Jun 2023"}, "employment_type": "Full-time", "company": {"id": "13591182", "name": "SSN College of Engineering", "url": "https://www.linkedin.com/company/13591182/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C510BAQHjX1EVumFLhA/company-logo_200_200/company-logo_200_200/0/1631359924922?e=**********&v=beta&t=c4BskBqApDBbntmnLx1X-bz4WtY3XIRbnv5s4qr8PrU", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C510BAQHjX1EVumFLhA/company-logo_100_100/company-logo_100_100/0/1631359924922?e=**********&v=beta&t=nzvAKqAZOs_QygzoiwLzqcQeUgqRpyLX93AngPB7mRg", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C510BAQHjX1EVumFLhA/company-logo_400_400/company-logo_400_400/0/1631359924922?e=**********&v=beta&t=mhB1PkgsqpvOFzJXoMMaV_jC5BF3p60iK1A_DKbScwY", "expires_at": **********000}]}}, {"title": "Human Resource Intern", "description": "-Valuable hands-on experience and practical knowledge in Human Resources\n-Gained useful Business experience, valuable knowledge and skills in recruitment, onboarding, and employee relations.\n-Performance Management\n-Administrative Responsibilities\n-Future Planning & Guiding\n-Diversity and Inclusion Equal Success", "location": "Pune, Maharashtra, India", "date": {"start": "Apr 2023", "end": "Jun 2023"}, "employment_type": "Full-time", "company": {"id": "14383287", "name": "PHN Technology Pvt Ltd", "url": "https://www.linkedin.com/company/14383287/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQHjzKoDm9aq7w/company-logo_200_200/company-logo_200_200/0/1724738277622?e=**********&v=beta&t=R7JGXLCfRtY1cGmCgGUMiEGtsu8TuW93y9kXGzGvJCc", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQHjzKoDm9aq7w/company-logo_100_100/company-logo_100_100/0/1724738277622?e=**********&v=beta&t=lW3cDI6s1ilrYWjsLYx-z9mMViTfZtC2mnYngBmeuVE", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQHjzKoDm9aq7w/company-logo_400_400/company-logo_400_400/0/1724738277622?e=**********&v=beta&t=4UzWPuW8KKC0kEjK6FHaALchtwE4ZcpRmb1RlM2lXRs", "expires_at": **********000}]}}, {"title": "ACM Winter School", "description": "Digital Trust\n-Coded real-world scenarios\n-Topics: Foundations of Security, Advanced Cryptography, Network Security, Hardware & System Security, Program Verification & Correctness, Privacy, Blockchains, Applications of AI in Security, State-of-the-art Industry perspective on security.", "date": {"start": "Dec 2022", "end": "Dec 2022"}, "employment_type": "Full-time", "company": {"id": "157266", "name": "Indian Institute of Technology, Bombay", "url": "https://www.linkedin.com/company/157266/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGmDoDCx_FYpA/company-logo_200_200/company-logo_200_200/0/1660636307357/indian_institute_of_technology_bombay_logo?e=**********&v=beta&t=fK7W5izvI8Pn2s7pRgp9cAL0fNsSsdOTTxC-z6KL_uE", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGmDoDCx_FYpA/company-logo_100_100/company-logo_100_100/0/1660636307357/indian_institute_of_technology_bombay_logo?e=**********&v=beta&t=GfaMuSgIAX6-Dsj0LetoyuyzrIg3hozpysPwoH_aeSo", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGmDoDCx_FYpA/company-logo_400_400/company-logo_400_400/0/1660636307357/indian_institute_of_technology_bombay_logo?e=**********&v=beta&t=qGDvwVxfv3EqA53xH9G9grl2agBDybnIEgGbUaC20AY", "expires_at": **********000}]}}, {"title": "Business Development Intern", "description": "-Analyze consumer behavior and anticipate market trends to develop solutions to consumer problems and needs.\n-Analyze the trends in the market and the company’s strategies to identify opportunities to cash in on.\n-Identify and Evaluate new customers to increase the company’s sales targets and marketing campaign audience.\n-Work to improve the customer feedback channels and communication to ensure a constant flow of reviews from them.\n-Identify and report lucrative market business opportunities to capitalize on and increase the market share.\n-Create ad-hoc reports for the sales and the management staff as requested.\n-Other duties as assigned by the business development analyst and/or manager.", "date": {"start": "May 2021", "end": "May 2021"}, "employment_type": "Full-time", "company": {"id": "13296566", "name": "LUDIFU (Let Us Do It For U)", "url": "https://www.linkedin.com/company/13296566/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQEBcba37MeFHg/company-logo_200_200/company-logo_200_200/0/1669079585634/ludifu_logo?e=**********&v=beta&t=mYfnxlYHrOpCcGM6G0WAvHybfIP54pcG3nydaUtOMHQ", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQEBcba37MeFHg/company-logo_100_100/company-logo_100_100/0/1669079585634/ludifu_logo?e=**********&v=beta&t=0sE5bqcBC9ti-grgETlZyy-yf9SN_K4-lxmoDjg0EGI", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQEBcba37MeFHg/company-logo_400_400/company-logo_400_400/0/1669079585634/ludifu_logo?e=**********&v=beta&t=6CR-6YN8NaDSI-huosT9BpRPwE-_i_y347Uh7XKytdM", "expires_at": **********000}]}}], "skills": [{"skill": "Machine Learning foundation"}, {"skill": "Cloud foundations"}, {"skill": "AI-ML"}, {"skill": "Artificial Intelligence: Machine Learning"}, {"skill": "AWS"}, {"skill": "AWS Cloud Computing"}, {"skill": "Machine Learning"}, {"skill": "ML Foundations"}, {"skill": "AWS Architecture"}, {"skill": "AWS Cloud"}, {"skill": "AWS Core Services"}, {"skill": "AWS Pricing"}, {"skill": "AWS Support"}, {"skill": "Problem Solving"}, {"skill": "Passionate about Work"}, {"skill": "Outreach Programs"}, {"skill": "Media Management"}, {"skill": "Marketing"}, {"skill": "Easily Adaptable"}, {"skill": "Brand Awareness"}], "certifications": [{"title": "AWS Academy Graduate - AWS Academy Machine Learning Foundations", "authority": "Amazon Web Services (AWS)", "credential_url": "https://www.credly.com/badges/0e0224d0-c2c6-4409-a2ac-4681976eda90/linked_in_profile", "issued_at": "Jun 2023"}, {"title": "Cyber Hygiene Practices", "authority": "Stay Safe Online Campaign (INDIA'S G20)", "credential_url": "https://infosecawareness.in/validate-certificate?certid=MeitY/ISEA/WCHP/030235", "issued_at": "Jun 2023"}, {"title": "AWS Academy Graduate - AWS Academy Cloud Foundations", "authority": "Amazon Web Services (AWS)", "credential_url": "https://www.credly.com/badges/de219ed9-8745-48e8-94b9-274875389d7c/linked_in_profile", "issued_at": "May 2023"}, {"title": "Professional in Human Resources: Business Planning", "authority": "Skillsoft", "credential_url": "https://skillsoft.digitalbadges.skillsoft.com/1e50e4f6-7e23-49a6-99cd-b85f68215f3c", "issued_at": "Feb 2023"}, {"title": "UI/ UX", "authority": "Great Learning", "credential_url": "https://verify.mygreatlearning.com/verify/YZPHARYS", "issued_at": "Aug 2022"}, {"title": "Advanced CPP Training", "authority": "Indian Institute of Technology, Bombay", "credential_url": null, "issued_at": "Jul 2022"}, {"title": "Cyber Security", "authority": "Infosys", "credential_url": "https://verify.onwingspan.com/", "issued_at": "Jul 2022"}, {"title": "Cybersecurity Virtual Experience Program (MasterCard)", "authority": "Forage", "credential_url": "https://forage-uploads-prod.s3.amazonaws.com/completion-certificates/mastercard/vcKAB5yYAgvemepGQ_Mastercard_RiPiHxXJji98Dz253_1657043796243_completion_certificate.pdf", "issued_at": "Jul 2022"}, {"title": "Python (Basic)", "authority": "HackerRank", "credential_url": "https://www.hackerrank.com/certificates/4e459bfa58c0", "issued_at": "Mar 2022"}, {"title": "A1 Level German Language", "authority": "Goethe Institut Indien", "credential_url": null, "issued_at": "Dec 2021"}, {"title": "AWS Cloud Practioner Essentials", "authority": "Amazon Web Services (AWS)", "credential_url": null, "issued_at": "Jul 2021"}, {"title": "Artificial Intelligence", "authority": "Accenture", "credential_url": "futurelearn.com/certificates/7tu750v", "issued_at": "Jul 2021"}, {"title": "Microsoft Certified Fundamental Enthusiast", "authority": "<PERSON><PERSON><PERSON><PERSON>", "credential_url": null, "issued_at": "Jul 2021"}, {"title": "Programming in C# Certification", "authority": "Udemy", "credential_url": "ude.my/UC-77faca2c-2559-421b-9259-f19afa14266f", "issued_at": "Jul 2021"}], "publications": [], "educations": [{"school": "Pimpri Chinchwad Education Trust'S. Pimpri Chinchwad College Of Engineering", "date": {"start": "Feb 2021", "end": "Jun 2024"}, "degree": "Bachelor of Technology - BTech in Computer Engineering ", "grade": "CGPA: 8.61"}, {"school": "Pimpri Chinchwad Education Trust'S. Pimpri Chinchwad College Of Engineering", "date": {"start": "Feb 2022", "end": "Jul 2024"}, "degree": "Honors in Cyber Security ", "grade": "CGPA: 9.3"}, {"school": "City Pride Junior College", "date": {"start": "Jun 2018", "end": "May 2020"}}, {"school": "City Pride School, Pune", "date": {"start": "Jul 2011", "end": "Jul 2017"}}], "honors": [], "volunteers": []}, {"id": "976513872", "urn": "ACoAADo0a1ABCZ5KfzTE1OtvX8myz3CZsLjnA9c", "public_identifier": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "first_name": "Darshan", "last_name": "<PERSON><PERSON><PERSON>", "full_name": "<PERSON><PERSON>", "headline": "I play with AI tools at Explorer’s Lab to help you build smarter with AI | UI/UX Designer | Product Designer", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": true, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1645855087143, "created_date": "2022-02-26T05:58:07.143Z", "location": {"country_code": "IN", "city": "India", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D4D03AQEJGN-6h4WG1w/profile-displayphoto-shrink_100_100/B4DZXylQhKH4AU-/0/1743531609315?e=**********&v=beta&t=yprd002hWFuY1vugXKohz4s9bjLRTnVWYBLn9quhxXc", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D4D03AQEJGN-6h4WG1w/profile-displayphoto-shrink_800_800/B4DZXylQhKH4Ac-/0/1743531609315?e=**********&v=beta&t=Hh0tDQlxxXFw4rWhJDp4eHqF44DZTwBWUsxOcoc2wZ0", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D03AQEJGN-6h4WG1w/profile-displayphoto-shrink_200_200/B4DZXylQhKH4AY-/0/1743531609315?e=**********&v=beta&t=j66iMXfBWJQCR4kZUehC2NO8zHOp3mKmgTnkKH0tO9k", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D4D03AQEJGN-6h4WG1w/profile-displayphoto-shrink_400_400/B4DZXylQhKH4Ag-/0/1743531609297?e=**********&v=beta&t=BzqOA64E-fbAlNfcA0qoCSQVJegYb2acQaX4ih0hX2I", "expires_at": **********000}], "cover": [{"width": 800, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D16AQFzolUpLAj7ZA/profile-displaybackgroundimage-shrink_200_800/B4DZXyl0TgG4AY-/0/1743531755211?e=**********&v=beta&t=djoRUY3Ge4NdKnPfYDXL9jJveCiQSs1w6JnIfyUdhYw", "expires_at": **********000}, {"width": 1400, "height": 350, "url": "https://media.licdn.com/dms/image/v2/D4D16AQFzolUpLAj7ZA/profile-displaybackgroundimage-shrink_350_1400/B4DZXyl0TgG4Ac-/0/1743531755211?e=**********&v=beta&t=_63J_BFO5iERJdPvBQoyXbjS-oLXZDDlwhUZ5faR8Es", "expires_at": **********000}], "associated_hashtag": ["#project", "#innovation", "#contentcreator", "#webdevelopment", "#entrepreneurship"], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "UI UX designer ", "location": "Ahmedabad, Gujarat, India · On-site", "date": {"start": "Jan 2025", "end": "May 2025"}, "employment_type": "Internship", "skills": ["Led UI/UX design across 2+ US-based client websites, building scalable Figma design systems with reusable components, variants, and responsive layouts to accelerate developer handoff.\n\nDeveloped an internal Web Story Maker tool that automated content formatting and slide structuring, reducing web story creation time by 50% and receiving team-wide appreciation.\n\nLaunched and managed an AI-powered video content channel (<PERSON><PERSON><PERSON>) using tools like Dreamina, Halelio AI, and CapCut — reaching 34 K+ views within 4 weeks of launch."], "company": {"id": "80961850", "name": "SEO Giants", "url": "https://www.linkedin.com/company/80961850/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQGHMnzXgEnWdA/company-logo_200_200/company-logo_200_200/0/1719256751970/seogiants_logo?e=**********&v=beta&t=EyC5Mzeq336o9hkKl7ycQQt3ZdBrRRuEzNAH5UWNrkk", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQGHMnzXgEnWdA/company-logo_100_100/company-logo_100_100/0/1719257469886/seogiants_logo?e=**********&v=beta&t=AJvii4I7aRbJRv3KfHwiBwDo2X1MhPiy-_PADK0Ehy8", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQGHMnzXgEnWdA/company-logo_400_400/company-logo_400_400/0/1719256501583/seogiants_logo?e=**********&v=beta&t=JEnyxmryRJVHgtQHZMDF-m2HyWuF29CtbEJtcqAD4p8", "expires_at": **********000}]}}, {"title": "UI/UX Designer", "description": "During my internship at Hotel Hub, I worked on the HBS product. I analyzed agent feedback and contributed to enhancing the map view of HBS. Additionally, I developed a new UI for HBS 3.0 and presented an AI solution to automate certain processes", "location": "Bengaluru, Karnataka, India · On-site", "date": {"start": "May 2024", "end": "Jul 2024"}, "employment_type": "Internship", "skills": ["Design Management", "Client Requirements", "User Experience Design (UED)", "Communication", "HTML", "Presentations", "Observation", "Figma (Software)", "Artificial Intelligence (AI)", "Cascading Style Sheets (CSS)", "User Experience (UX)", "Adaptation", "Project Initiation"], "company": {"id": "13345867", "name": "HotelHub", "url": "https://www.linkedin.com/company/13345867/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4E0BAQGoG_M1f0Y-KQ/company-logo_200_200/company-logo_200_200/0/1701346431468/hotelhub_logo?e=**********&v=beta&t=vTrBCuTFgbOno7bCCHtRXhRuXJS7MbEPx3lvyHX0mKM", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D4E0BAQGoG_M1f0Y-KQ/company-logo_100_100/company-logo_100_100/0/1701346431468/hotelhub_logo?e=**********&v=beta&t=FfLggpBfxai3hWBBbrjW8U-kxI5FqMnC1gXl43q_MZM", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D4E0BAQGoG_M1f0Y-KQ/company-logo_400_400/company-logo_400_400/0/1701346431468/hotelhub_logo?e=**********&v=beta&t=AKOnruQ19oPX7azHcXZrRvyRqy-Ufdg5ay20mlxJmNs", "expires_at": **********000}]}, "media": [{"description": "During my internship at Hotel Hub, I worked on the HBS product. I analyzed agent feedback and contributed to enhancing the map view of HBS. Additionally, I developed a new UI for HBS 3.0 and presented an AI solution to automate certain processes.", "thumbnail": [{"width": 909, "height": 1286, "url": "https://media.licdn.com/dms/image/v2/D4D2DAQGbjSh3BEcMgw/profile-treasury-document-cover-images_1280/profile-treasury-document-cover-images_1280/0/1720480431125?e=**********&v=beta&t=mipro_xojzbKNknCF-OZiDJESsKPyepamPHPWPSImZA", "expires_at": **********000}, {"width": 570, "height": 806, "url": "https://media.licdn.com/dms/image/v2/D4D2DAQGbjSh3BEcMgw/profile-treasury-document-cover-images_800/profile-treasury-document-cover-images_800/0/1720480431678?e=**********&v=beta&t=leTcKTOGyXy31tXa2QILGSmZxgd5ooNXlw5xbSZaxuI", "expires_at": **********000}, {"width": 1364, "height": 1929, "url": "https://media.licdn.com/dms/image/v2/D4D2DAQGbjSh3BEcMgw/profile-treasury-document-cover-images_1920/profile-treasury-document-cover-images_1920/0/1720480431088?e=**********&v=beta&t=7nc4QCs0xHqhh4jvnwFrOR8FK7URCZzKRbGOt3FbtsU", "expires_at": **********000}, {"width": 347, "height": 491, "url": "https://media.licdn.com/dms/image/v2/D4D2DAQGbjSh3BEcMgw/profile-treasury-document-cover-images_480/profile-treasury-document-cover-images_480/0/1720480430520?e=**********&v=beta&t=rxBOvW0sUK_7nOpG-Bn6X-vX_qqfvEUiMveoSQPlFE4", "expires_at": **********000}], "title": "Internship Completion Letter Darshan_DSC.pdf"}]}, {"title": "Graphic Designer", "description": "Designed 30+ posts, driving a 200% increase in revenue by aligning visuals with customer needs and business goals.\nLeveraged data insights to refine strategies, balancing creativity with metrics to boost engagement and conversions.", "location": "Ahmedabad, Gujarat, India · Hybrid", "date": {"start": "Sep 2023", "end": "May 2024"}, "employment_type": "Freelance", "skills": ["Business Strategy", "Graphic Design", "Film", "Social Media", "Data Analysis"], "company": {"name": "Class Realty Solutions", "url": "https://www.linkedin.com/search/results/all/?keywords=Class+Realty+Solutions"}}, {"title": "Founder", "description": "Designed and launched an AI-driven platform with interactive celebrity-inspired bots and received 10+ positive feedbacks.\nGained hands-on experience in balancing functional design and user needs, enhancing the platform’s usability and appeal.", "location": "Ahmedabad, Gujarat, India · On-site", "date": {"start": "Jun 2023", "end": "Sep 2023"}, "employment_type": "Self-employed", "skills": ["HTML", "Cascading Style Sheets (CSS)", "JavaScript", "Business Strategy", "Client Requirements", "Problem Solving", "User Experience Design (UED)", "Product Development", "Product Design"], "company": {"name": "Celeb AI", "url": "https://www.linkedin.com/search/results/all/?keywords=Celeb+AI"}}], "skills": [{"skill": "Gen AI"}, {"skill": "Adobe Photoshop"}, {"skill": "AI designer"}, {"skill": "figma"}, {"skill": "Empathic Design"}, {"skill": "User-centered Design"}, {"skill": "UX Research"}, {"skill": "Product Development"}, {"skill": "Product Design"}, {"skill": "Business Strategy"}, {"skill": "Graphic Design"}, {"skill": "Film"}, {"skill": "Social Media"}, {"skill": "Data Analysis"}, {"skill": "Design Management"}, {"skill": "Observation"}, {"skill": "Oral Communication"}, {"skill": "Creative Ideation"}, {"skill": "Project Initiation"}, {"skill": "Presentations"}], "certifications": [{"title": "Flipkart Grid", "authority": "Unstop", "credential_url": "https://unstop.com/certificate-preview/326d0e8f-df9f-45cc-ab91-b8072864687c?utm_campaign", "issued_at": "Aug 2024"}, {"title": "Principles of UX/UI Design", "authority": "Meta", "credential_url": "https://www.coursera.org/account/accomplishments/records/N5MSNM06CHDP", "issued_at": "Aug 2024"}], "publications": [], "educations": [{"school": "Pandit Deendayal Energy University", "date": {"start": "2021", "end": "2025"}, "degree": "Bachelor of Technology - BTech, Computer Engineering", "grade": "9.72 C.G.P.A", "skills": ["Presentations", "Web Technologies", "Computer Science", "Communication", "Programming Languages", "JavaScript", "Cloud Computing", "Software Development", "Programming", "Problem Solving"]}, {"school": "<PERSON><PERSON>ee <PERSON>n School - B<PERSON>j", "date": {"start": "May 2017", "end": "May 2019"}, "grade": "89%ile"}, {"school": "Army Public School (APS)", "date": {"start": "Apr 2007", "end": "Apr 2017"}, "degree": "1-10 Standard", "grade": "A2", "skills": ["Mathematics", "English", "Science", "Oral Communication"]}], "honors": [], "volunteers": []}, {"id": "*********", "urn": "ACoAACVEEOsBWKNyqCRBkZF7qFeTyQa00ernXyI", "public_identifier": "priya-jain-a0b473155", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON>", "full_name": "<PERSON><PERSON>", "headline": "Summer Intern at VTION || MBA- Business Analytics (2023 - 25) || School of Business Management - NMIMS || Electronics and Communication Engineering", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": true, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1512661282094, "created_date": "2017-12-07T15:41:22.094Z", "location": {"country": "India", "country_code": "IN", "city": "Mumbai, Maharashtra, India", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D4D03AQEpf6UMlk4B5Q/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/1720846480005?e=**********&v=beta&t=vtpolXA3cv9G7JWkP-TD83gSREWxtjKC8LXFM3nN2wM", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D03AQEpf6UMlk4B5Q/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1720846480005?e=**********&v=beta&t=x6JVc-nqGle5xQLXnIdTVN9huDgCnLxA6LSqhwewBUo", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D4D03AQEpf6UMlk4B5Q/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1720846480005?e=**********&v=beta&t=PiFOhrMrobbAGRiGHoEI3Y4q1WcxA134MViEFchaPPg", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D4D03AQEpf6UMlk4B5Q/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1720846480033?e=**********&v=beta&t=6wLZK69ygmWvuxvn8RGXwA9w0GiDB3MvFAweIWAGbJ4", "expires_at": **********000}], "cover": [{"width": 800, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D16AQHjz6U0ySbzHw/profile-displaybackgroundimage-shrink_200_800/profile-displaybackgroundimage-shrink_200_800/0/1689222997040?e=**********&v=beta&t=YfSL9u-Z7Uvhw_9les9KewXVSp6xUxGysWFqaWI3muU", "expires_at": **********000}, {"width": 1080, "height": 270, "url": "https://media.licdn.com/dms/image/v2/D4D16AQHjz6U0ySbzHw/profile-displaybackgroundimage-shrink_350_1400/profile-displaybackgroundimage-shrink_350_1400/0/1689222997040?e=**********&v=beta&t=wTXvw_IrFSOuTwGgXKFMzb1TQAmubULHW4XSNdWFzyA", "expires_at": **********000}], "associated_hashtag": [], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Data Analyst", "description": "Skills: Market Research · Microsoft Excel · Machine Learning · Natural Language Processing (NLP)", "location": "Mumbai, Maharashtra, India · Hybrid", "date": {"start": "Feb 2024", "end": "Jun 2024"}, "employment_type": "Internship", "company": {"id": "14406445", "name": "VTION Digital", "url": "https://www.linkedin.com/company/14406445/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQHTTtgmP3Qj3w/company-logo_200_200/company-logo_200_200/0/1686914488307/vtion_media_logo?e=**********&v=beta&t=aqT-hcWFyw_v6Cyql42zVPNSLAo0WYYNJ8zbkz-9pS4", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQHTTtgmP3Qj3w/company-logo_100_100/company-logo_100_100/0/1686914488307/vtion_media_logo?e=**********&v=beta&t=LUhDfjxO9w5jYdMz0LOf2-lXW2pR3gRSHJ-wNfmTC6E", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQHTTtgmP3Qj3w/company-logo_400_400/company-logo_400_400/0/1686914488307/vtion_media_logo?e=**********&v=beta&t=5UmNzDYqO1pxZOaIV5VGGfDSCVSqwDwVpWpT9Cz-138", "expires_at": **********000}]}}], "skills": [{"skill": "Analytics"}, {"skill": "Analytic Problem Solving"}, {"skill": "Market Research"}, {"skill": "Machine Learning"}, {"skill": "Natural Language Processing (NLP)"}, {"skill": "Analytical Skills"}, {"skill": "Presentations"}, {"skill": "Data Manipulation"}, {"skill": "Business Relationship Management"}, {"skill": "Marketing"}, {"skill": "Exploratory Data Analysis"}, {"skill": "Data Visualization"}, {"skill": "Business Analysis"}, {"skill": "<PERSON><PERSON>"}, {"skill": "Statistical Data Analysis"}, {"skill": "JMP"}, {"skill": "MySQL"}, {"skill": "Python (Programming Language)"}, {"skill": "Microsoft Excel"}, {"skill": "Soft Skills"}], "certifications": [{"title": "SQL Advanced", "authority": "HackerRank", "credential_url": "https://www.hackerrank.com/certificates/373a2c38e1e3", "issued_at": "Jul 2024"}, {"title": "Bloomberg Market Concept", "authority": "Bloomberg", "credential_url": null, "issued_at": "Jul 2023"}, {"title": "Fundamentals of Digital Marketing", "authority": "Google Digital Garage ", "credential_url": null, "issued_at": "Jul 2023"}, {"title": "KPMG-certified Lean Six Sigma Green Belt", "authority": "KPMG India", "credential_url": null, "issued_at": "Jul 2023"}], "publications": [], "educations": [{"school": "SVKM's <PERSON><PERSON><PERSON> Institute of Management Studies (NMIMS)", "date": {"start": "Jun 2023", "end": "Mar 2025"}, "degree": "Master of Business Administration - MBA, Business Analysis", "grade": "3.30", "skills": ["JMP", "Analytical Skills", "MySQL", "Business Analysis", "<PERSON><PERSON>", "Statistical Data Analysis"]}, {"school": "Jabalpur Engineering College", "date": {"start": "Aug 2018", "end": "Jun 2022"}, "degree": "Bachelor of Technology - BTech, Electrical, Electronic and Communications Engineering Technology/Technician", "grade": "7.90/10"}, {"school": "Vatsalya Senior Secondary School - India", "date": {"start": "Mar 2017", "end": "May 2018"}, "degree": "Higher secondary school, Science", "grade": "90.4%"}, {"school": "Vatsalya Senior Secondary School - India", "date": {}, "degree": "10th", "grade": "10/10 cgpa"}], "honors": [], "volunteers": []}, {"id": "988672231", "urn": "ACoAADrt8OcBJHcBojjnuAaR6eOr--uCgysw0ak", "public_identifier": "elorachung", "first_name": "Elora", "last_name": "<PERSON>", "full_name": "<PERSON><PERSON>", "headline": "Business @ Western University", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1649336597690, "created_date": "2022-04-07T13:03:17.690Z", "location": {"country": "Canada", "country_code": "CA", "city": "Richmond Hill, Ontario, Canada", "postal_code": null}, "avatar": [{"width": 200, "height": 199, "url": "https://media.licdn.com/dms/image/v2/D4D03AQGJYwqT175jbg/profile-displayphoto-shrink_200_200/B4DZTPFyb3HYAY-/0/1738641189365?e=**********&v=beta&t=kU4quRmy1dTLFdgh_N1QVgJ_PaDWYuU_12tqBd8hFUs", "expires_at": **********000}, {"width": 400, "height": 399, "url": "https://media.licdn.com/dms/image/v2/D4D03AQGJYwqT175jbg/profile-displayphoto-shrink_400_400/B4DZTPFyb3HYAg-/0/1738641189365?e=**********&v=beta&t=5p0cuvAli_FMgNMDJspZ68xaG6zS2nh7WLlyjD50KUo", "expires_at": **********000}, {"width": 100, "height": 99, "url": "https://media.licdn.com/dms/image/v2/D4D03AQGJYwqT175jbg/profile-displayphoto-shrink_100_100/B4DZTPFyb3HYAU-/0/1738641189365?e=**********&v=beta&t=FxzMiO4ChvCCqcIRnUUFUay8TeQmTu9fYE7KoxxtWOc", "expires_at": **********000}, {"width": 751, "height": 750, "url": "https://media.licdn.com/dms/image/v2/D4D03AQGJYwqT175jbg/profile-displayphoto-shrink_800_800/B4DZTPFyb3HYAc-/0/1738641189374?e=**********&v=beta&t=DUud8sR9QuBF2poZczdoEoYxc_5wqaL9T0G-J1pG5lM", "expires_at": **********000}], "cover": [{"width": 880, "height": 220, "url": "https://media.licdn.com/dms/image/v2/D4E16AQGOTRyIXxqRHg/profile-displaybackgroundimage-shrink_350_1400/B4EZV9zmc5HMAY-/0/1741572434097?e=**********&v=beta&t=VA9ZjfLLA1c9_DFR4xN2TAximQO-8dmWYkatciAv_OE", "expires_at": **********000}, {"width": 800, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4E16AQGOTRyIXxqRHg/profile-displaybackgroundimage-shrink_200_800/B4EZV9zmc5HMAU-/0/1741572434097?e=**********&v=beta&t=k_z1h-JCVA-KTUTeWr_jKhqpSFU29oFowoasKe_HPoI", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Food Server", "location": "Richmond Hill, Ontario, Canada · On-site", "date": {"start": "Jun 2023", "end": "Aug 2024"}, "employment_type": "Seasonal", "company": {"id": "********", "name": "The Flying Fish Cafe & Sushi Bar", "url": "https://www.linkedin.com/company/********/"}}, {"title": "Food Server", "location": "Richmond Hill · On-site", "date": {"start": "Jun 2022", "end": "Nov 2022"}, "employment_type": "Contract Full-time", "company": {"id": "********", "name": "<PERSON> and <PERSON><PERSON>", "url": "https://www.linkedin.com/company/********/"}}, {"title": "Accounts Payable Coordinator", "location": "Richmond Hill, Ontario, Canada · Hybrid", "date": {"start": "Oct 2021", "end": "Jan 2022"}, "employment_type": "Co-op", "company": {"id": "71989", "name": "Venterra Realty", "url": "https://www.linkedin.com/company/71989/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C560BAQF0Iub9dg9WQg/company-logo_200_200/company-logo_200_200/0/*************?e=**********&v=beta&t=2elK_wgkEc7iXLHpWZhyv4gP1KTcPJUumN_x7sAMCI4", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C560BAQF0Iub9dg9WQg/company-logo_100_100/company-logo_100_100/0/*************?e=**********&v=beta&t=SFN62ZOY0JJ0ci4IpZGNpov_qOQHQ6lDLHBdhc3nZZw", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C560BAQF0Iub9dg9WQg/company-logo_400_400/company-logo_400_400/0/*************?e=**********&v=beta&t=nknoVNZ2tVEqbWrZupEgR05E6Pyv_a-AwRGhOLh9ep0", "expires_at": **********000}]}}], "skills": [], "certifications": [{"title": "SmartServe", "authority": "Smart Serve Ontario", "credential_url": null, "issued_at": "Mar 2025 · Expires Mar 2030"}, {"title": "Experience a Day with KPMG Audit & Assurance", "authority": "KPMG", "credential_url": null, "issued_at": "Sep 2024"}, {"title": "Learn to Lead", "authority": "Western University", "credential_url": null, "issued_at": "Jan 2024"}, {"title": "CPR/AED/First Aid", "authority": "St. John Ambulance Ontario", "credential_url": null, "issued_at": "Jun 2023"}, {"title": "SHSM in Arts and Culture", "authority": "York Region District School Board", "credential_url": null, "issued_at": "Jun 2023"}], "publications": [], "educations": [{"school": "Western University", "date": {"start": "Sep 2023", "end": "Apr 2027"}, "degree": "Undergraduate, BMOS, Specialization in Human Resources"}, {"school": "Richmond Hill High School (Ontario)", "date": {"start": "Sep 2019", "end": "Jun 2023"}, "degree": "High School Diploma"}], "honors": [{"title": "Certificate of Merit", "issued_by": "Richmond Hill High School Music Department ", "associated_with": "Richmond Hill High School (Ontario)"}, {"title": "Certificate of Merit ", "issued_by": "Richmond Hill High School Music Department ", "associated_with": "Richmond Hill High School (Ontario)"}], "volunteers": [{"organization": "Western University Korean Student Association", "title": "Co-VP of Marketing", "duration": "3 mos", "date": {"start": "Apr 2025", "end": "Present"}}, {"organization": "University Students'​ Council at Western University", "title": "Logistics Volunteer", "duration": "2 mos", "description": "Community Volunteer Tax Income Program (CVITP)", "date": {"start": "Mar 2025", "end": "Apr 2025"}}, {"organization": "Western Self-Improvement Club", "title": "VP of Communications ", "duration": "7 mos", "date": {"start": "Oct 2024", "end": "Apr 2025"}}, {"organization": "Western University Korean Student Association", "title": "Marketing Executive", "duration": "8 mos", "date": {"start": "Sep 2024", "end": "Apr 2025"}}, {"organization": "Dirty Girl Mud Run", "title": "Start & Finish Line Volunteer", "cause": "Health", "duration": "1 mo", "media": [{"description": null, "thumbnail": [{"width": 800, "height": 533, "url": "https://media.licdn.com/dms/image/sync/v2/D4E27AQGQGfl9ZHcO0g/articleshare-shrink_800/articleshare-shrink_800/0/1734534686525?e=**********&v=beta&t=TK-cIWPFU81q6z-moTBUXmHxgdoF8sMCUUcgOgcYSfo", "expires_at": **********000}, {"width": 1080, "height": 720, "url": "https://media.licdn.com/dms/image/sync/v2/D4E27AQGQGfl9ZHcO0g/articleshare-shrink_1280_800/articleshare-shrink_1280_800/0/1734534686525?e=**********&v=beta&t=011rplodMsmDhD01UBuMH0RnLF-DAHAtHgi7RBJ3Fv4", "expires_at": **********000}, {"width": 160, "height": 106, "url": "https://media.licdn.com/dms/image/sync/v2/D4E27AQGQGfl9ZHcO0g/articleshare-shrink_160/articleshare-shrink_160/0/1734534686505?e=**********&v=beta&t=Qcc1iE2tzP6zqcbGCbylLWjj-1xuNS2eoMJ_lCOUiQE", "expires_at": **********000}, {"width": 480, "height": 320, "url": "https://media.licdn.com/dms/image/sync/v2/D4E27AQGQGfl9ZHcO0g/articleshare-shrink_480/articleshare-shrink_480/0/1734534686505?e=**********&v=beta&t=VKwRBXtZzUJgVsSm7X8N0VWEiSOB_fBhyGeEhJTdhTs", "expires_at": **********000}], "title": "MUDGIRL on Instagram: \"Join the heart of the MUDGIRL movement! 🌟 Our volunteers are the heartbeat of every event, bringing enthusiasm and support that make each muddy adventure unforgettable. Whether you're passionate about empowering women, fostering community spirit, or simply love getting your hands dirty (literally!), volunteering with MUDGIRL is your chance to make a splash. Dive into the mu"}]}, {"organization": "MusicFest Canada", "title": "Stage B Performer Guide", "cause": "Education", "duration": "1 mo", "media": [{"description": null, "thumbnail": [{"width": 800, "height": 600, "url": "https://media.licdn.com/dms/image/v2/D4D2DAQFN9EzAxNkz7w/profile-treasury-image-shrink_800_800/profile-treasury-image-shrink_800_800/0/1716218756721?e=**********&v=beta&t=tSMVVZpQ8TWf1ABmnBlXSsyy2az2J6-4_UlxGND5874", "expires_at": **********000}, {"width": 2048, "height": 1536, "url": "https://media.licdn.com/dms/image/v2/D4D2DAQFN9EzAxNkz7w/profile-treasury-image-shrink_8192_8192/profile-treasury-image-shrink_8192_8192/0/1716218756721?e=**********&v=beta&t=byKIJaiBsHLdnktwxPp7kBFec3lZ8RdV3hC-D0hA8kU", "expires_at": **********000}, {"width": 480, "height": 360, "url": "https://media.licdn.com/dms/image/v2/D4D2DAQFN9EzAxNkz7w/profile-treasury-image-shrink_480_480/profile-treasury-image-shrink_480_480/0/1716218756721?e=**********&v=beta&t=pnvJmwF_yzaZo3fL8IIQ-uK2CNt54VZ897aqQMbajAY", "expires_at": **********000}, {"width": 1280, "height": 960, "url": "https://media.licdn.com/dms/image/v2/D4D2DAQFN9EzAxNkz7w/profile-treasury-image-shrink_1280_1280/profile-treasury-image-shrink_1280_1280/0/1716218756721?e=**********&v=beta&t=ox1hiz4m3204Sz9LYhnNA-hLxvgsSwBSEcrW9um8MWU", "expires_at": **********000}, {"width": 1920, "height": 1440, "url": "https://media.licdn.com/dms/image/v2/D4D2DAQFN9EzAxNkz7w/profile-treasury-image-shrink_1920_1920/profile-treasury-image-shrink_1920_1920/0/1716218756721?e=**********&v=beta&t=cbcwPv5bc1fNTiQotARh4ZojqRUb4qLRKmQDZ-uTdqQ", "expires_at": **********000}, {"width": 160, "height": 120, "url": "https://media.licdn.com/dms/image/v2/D4D2DAQFN9EzAxNkz7w/profile-treasury-image-shrink_160_160/profile-treasury-image-shrink_160_160/0/1716218756721?e=**********&v=beta&t=YB0n4jwc1nR_N_PEsI8A5Vn7RAf2AQxp4KPMBIVw0qc", "expires_at": **********000}], "title": "Stage B Volunteers"}]}, {"organization": "Vocal Fusion", "title": "Co-President", "cause": "Arts and Culture", "duration": "5 mos", "date": {"start": "Sep 2022", "end": "Jan 2023"}}]}, {"id": "723654574", "urn": "ACoAACsiF64BguPprcYSUYaEdNcf-vD_1uWz5uU", "public_identifier": "jyosna-s-6ba425182", "first_name": "Jyosna", "last_name": "S", "full_name": "Jyosna S", "headline": "Master's student in Computer Science @RIT | Data engineering intern @Amazon| 2 yrs @Accenture as Assoc. Software Engineer", "is_premium": false, "is_open_to_work": true, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1552713372035, "created_date": "2019-03-16T05:16:12.035Z", "location": {"country": "United States", "country_code": "US", "city": "Rochester, New York, United States", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D5603AQEkz6LBYvUaXw/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/1676021910284?e=**********&v=beta&t=tbn7hPhYQhv9j-l2vfUx994B5Zc1A5NOu7uYm7TphkI", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5603AQEkz6LBYvUaXw/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1676021910284?e=**********&v=beta&t=-VAXy_tHyl4ujdPch8CZI5cuAvpw3nMFCVfeWG1PIFk", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D5603AQEkz6LBYvUaXw/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1676021910284?e=**********&v=beta&t=W9Stzu0rB9DGDiGkd_2ykjm4s0dvIquOV0FZV80n07I", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D5603AQEkz6LBYvUaXw/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1676021910284?e=**********&v=beta&t=gk33abDzGfZbX0DPxa482Dht-7QtxJCbvYdZUw7x_ZA", "expires_at": **********000}], "cover": [{"width": 800, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5616AQFaRCfJf-GKZg/profile-displaybackgroundimage-shrink_200_800/profile-displaybackgroundimage-shrink_200_800/0/1691334665848?e=**********&v=beta&t=IJSp5FJtp6GmNRxnHE60IXRUuc0KC1HpmCnTs_rEmYQ", "expires_at": **********000}, {"width": 1284, "height": 321, "url": "https://media.licdn.com/dms/image/v2/D5616AQFaRCfJf-GKZg/profile-displaybackgroundimage-shrink_350_1400/profile-displaybackgroundimage-shrink_350_1400/0/1691334665848?e=**********&v=beta&t=lDE87WGtcO-8Zaq4tjmjn2KUXfK0tffWEXaibmP0lu4", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Data Engineering intern", "description": "Skills: Data Warehousing · Software Documentation · Presentation Skills · Problem Solving · Communication · Git", "location": "Bellevue, Washington, United States · On-site", "date": {"start": "Jun 2024", "end": "Aug 2024"}, "employment_type": "Full-time", "company": {"id": "1586", "name": "Amazon", "url": "https://www.linkedin.com/company/1586/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C560BAQHTvZwCx4p2Qg/company-logo_200_200/company-logo_200_200/0/1630640869849/amazon_logo?e=**********&v=beta&t=foK4RFzqFsrw-0kQyMkfIAptSxuwm5tH9P6t89szHc4", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C560BAQHTvZwCx4p2Qg/company-logo_100_100/company-logo_100_100/0/1630640869849/amazon_logo?e=**********&v=beta&t=NHBsCs31X6QrKD-UKKP6S2ljdJ2PYlkRSeMaspJG-6E", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C560BAQHTvZwCx4p2Qg/company-logo_400_400/company-logo_400_400/0/1630640869849/amazon_logo?e=**********&v=beta&t=i04pZ-FG84r5ypA7UKvYI8pc7GU7Ids6Um4w4Q1nLI8", "expires_at": **********000}]}}, {"title": "Software Engineering", "description": "Skills: SQL · React.js · PL/SQL · Tableau · Microsoft Azure · Presentation Skills · Informatica PowerCenter · ETL · Informatica", "location": "Bengaluru, Karnataka, India", "date": {"start": "Jun 2021", "end": "Jul 2023"}, "employment_type": "Full-time", "company": {"id": "9215331", "name": "Accenture in India", "url": "https://www.linkedin.com/company/9215331/", "logo": [{"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQHCNiZ7blKfhA/company-logo_400_400/B56ZcfITMHG0AY-/0/1748573962960/accentureindia_logo?e=**********&v=beta&t=QgIBWJ2-mXCWCXoyBhTQBBIq4Op2C2dvkrekeb83Y6E", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQHCNiZ7blKfhA/company-logo_200_200/B56ZcfITMHG0AI-/0/1748573962960/accentureindia_logo?e=**********&v=beta&t=1G7pSaNcPs6iDc061st6bcLPKEyvsBZ9fyWYZ-1KduU", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQHCNiZ7blKfhA/company-logo_100_100/B56ZcfITMHG0AQ-/0/1748573962960/accentureindia_logo?e=**********&v=beta&t=3g40GXcS988l2n09MCeof8nFNVRn_YgIv_-6u2gU7xY", "expires_at": **********000}]}}], "skills": [{"skill": "XHTML"}, {"skill": "React"}, {"skill": "XML"}, {"skill": "Big Data Analytics"}, {"skill": "Machine Learning Algorithms"}, {"skill": "Big Data"}, {"skill": "Artificial Intelligence (AI)"}, {"skill": "Computational programming with python"}, {"skill": "Object oriented programming with Java"}, {"skill": "Algorithms"}, {"skill": "React.js"}, {"skill": "Git"}, {"skill": "Presentation Skills"}, {"skill": "Software Documentation"}, {"skill": "Data Warehousing"}, {"skill": "Problem Solving"}, {"skill": "Communication"}, {"skill": "Apache Kafka"}, {"skill": "ETL"}, {"skill": "Informatica"}], "certifications": [{"title": "Google Data Analytics", "authority": "Google", "credential_url": "https://www.coursera.org/account/accomplishments/specialization/certificate/EKZWCHCHNB7A", "issued_at": "Mar 2023"}], "publications": [], "educations": [{"school": "Rochester Institute of Technology", "date": {"start": "Aug 2023", "end": "Aug 2025"}, "degree": "Masters, Computer Science", "grade": "3.96/4", "skills": ["React", "XHTML", "Computational programming with python", "Artificial Intelligence (AI)", "Machine Learning Algorithms", "Object oriented programming with Java", "Big Data Analytics", "Algorithms", "XML", "Big Data"]}, {"school": "PES University", "date": {"start": "2017", "end": "2021"}, "degree": "Bachelor of Technology - BTech, Computer Science", "grade": "8.28/10"}], "honors": [], "volunteers": [{"organization": "CSR Club PES University", "title": "Core", "cause": "Social Services", "duration": "1 yr 9 mos", "description": "As a part of CSR team , me and team helped in managing events like Blood donation camp, Swachh Bharath Champaign, School visit . ", "date": {"start": "Sep 2018", "end": "May 2020"}}]}, {"id": "775536853", "urn": "ACoAAC45wNUBQrIR7X76M4Tll0uH006IrBcuwPA", "public_identifier": "priya-patel-ms2", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON>, <PERSON>", "full_name": "<PERSON><PERSON>, MS", "headline": "Business Intelligence Developer I", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1574986726338, "created_date": "2019-11-29T00:18:46.338Z", "location": {"country": "United States", "country_code": "US", "city": "Canton, Ohio, United States", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D4E03AQEQAod1ZB73Ng/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/1674160440020?e=**********&v=beta&t=TjgiqInOnECf58CnHQs8DH7-TG_jUlcUPIjst38C_-c", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4E03AQEQAod1ZB73Ng/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1674160440020?e=**********&v=beta&t=F6kNNX5iNDK0xzvgl7HWDeblmOvx37yfijQXD_dIWig", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D4E03AQEQAod1ZB73Ng/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1674160440020?e=**********&v=beta&t=dGGVDTXkOEgqjbGwmrkeUI2HvwNofa9HhQ85-UTeVEs", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D4E03AQEQAod1ZB73Ng/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1674160440020?e=**********&v=beta&t=yaWoT-gsZsvBknScJPimuaDp6CMIT-LRxeiDEYgfLd0", "expires_at": **********000}], "cover": [{"width": 800, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4E16AQFCDluRg9-QVA/profile-displaybackgroundimage-shrink_200_800/profile-displaybackgroundimage-shrink_200_800/0/1731188231634?e=**********&v=beta&t=IriKnW5_fTkBmmiMom6szwqGXXwqYuemuV0CldeQTsE", "expires_at": **********000}, {"width": 1400, "height": 350, "url": "https://media.licdn.com/dms/image/v2/D4E16AQFCDluRg9-QVA/profile-displaybackgroundimage-shrink_350_1400/profile-displaybackgroundimage-shrink_350_1400/0/1731188231634?e=**********&v=beta&t=F1TjxoM79MKA0dC5UHed0yIP2q-2bfYDW5hTlgURTTY", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Business Intelligence Developer I ", "description": "Skills: Citizen Developer", "date": {"start": "Apr 2024", "end": "Present"}, "employment_type": "Full-time", "company": {"id": "166625", "name": "University Hospitals", "url": "https://www.linkedin.com/company/166625/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C510BAQGBOroMW1T3Ww/company-logo_200_200/company-logo_200_200/0/1631377076486?e=**********&v=beta&t=zM2-_MiRQbrKScXDqa6TYmFXGURmnr1iqSFP91aKXvI", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C510BAQGBOroMW1T3Ww/company-logo_100_100/company-logo_100_100/0/1631377076486?e=**********&v=beta&t=UhpJ7u-uDLt5Q3ckivEOyoBQUu4H0CQF4ZKIqYIQ4eQ", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C510BAQGBOroMW1T3Ww/company-logo_400_400/company-logo_400_400/0/1631377076486?e=**********&v=beta&t=OTaNfaaiT1XB7uDI_ggn3DOV3VPYJm2eb94f9IFJk6c", "expires_at": **********000}]}}, {"title": "Medical Scribe", "location": "Canton, Ohio, United States", "date": {"start": "Oct 2019", "end": "May 2023"}, "employment_type": "Full-time", "company": {"id": "57745183", "name": "NOVUS HEALTH AND WELLNESS", "url": "https://www.linkedin.com/company/57745183/"}}, {"title": "Research Assistant", "location": "Greater Cleveland", "date": {"start": "2018", "end": "2019"}, "company": {"id": "59795746", "name": "Cleveland Clinic Lerner Research Institute", "url": "https://www.linkedin.com/company/59795746/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGL_mHpEW2M7g/company-logo_200_200/company-logo_200_200/0/1641838696638/cleveland_clinic_lerner_research_institute_logo?e=**********&v=beta&t=QqcgbkzAx8kbN4CyC1FWHkrGZC_aAlH9P1YulgFlB_w", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGL_mHpEW2M7g/company-logo_100_100/company-logo_100_100/0/1641838696638/cleveland_clinic_lerner_research_institute_logo?e=**********&v=beta&t=zLs_r4cK982-T5YGO95DY58JmpAUmXsWIzO0lpJ-Hss", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGL_mHpEW2M7g/company-logo_400_400/company-logo_400_400/0/1641838696638/cleveland_clinic_lerner_research_institute_logo?e=**********&v=beta&t=LDOp8YM0mUaHx085wlzTJtuCealPK3COdRn1rpqZxbQ", "expires_at": **********000}]}}, {"title": "Certified Nursing Assistant", "location": "Mayfield Heights, Ohio, United States", "date": {"start": "2017", "end": "2018"}, "company": {"id": "5656", "name": "Cleveland Clinic", "url": "https://www.linkedin.com/company/5656/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQHpIFVMDGreYg/company-logo_200_200/company-logo_200_200/0/1631310523369?e=**********&v=beta&t=0Fs48ENvnWJkdKEHFvVyvwKPNPO3R5rBN79nGvY0F98", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQHpIFVMDGreYg/company-logo_100_100/company-logo_100_100/0/1631310523369?e=**********&v=beta&t=z3SBUniE7zOwjIwa_GXF9eXWGMA_fZry4VepAuLscBA", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQHpIFVMDGreYg/company-logo_400_400/company-logo_400_400/0/1631310523369?e=**********&v=beta&t=xUqjreLfuBQd3qocWSrDjIMOUH3ahwD6kUZhB8uyhlU", "expires_at": **********000}]}}], "skills": [{"skill": "Beaker"}, {"skill": "<PERSON>"}, {"skill": "Git", "num_endorsements": 1}, {"skill": "Cascading Style Sheets (CSS)", "num_endorsements": 1}, {"skill": "PostgreSQL", "num_endorsements": 1}, {"skill": "React.js"}, {"skill": "JavaScript", "num_endorsements": 1}, {"skill": "HTML", "num_endorsements": 1}, {"skill": "Python (Programming Language)", "num_endorsements": 1}, {"skill": "Microsoft SQL Server"}, {"skill": "SQL"}, {"skill": "Azure SQL"}, {"skill": "Microsoft Power BI"}, {"skill": "Epic beaker"}, {"skill": "Epic reporting"}, {"skill": "Epic Citizen Developer"}], "certifications": [{"title": "Beaker Anatomic Pathology", "authority": "Epic", "credential_url": null, "issued_at": "Jan 2025"}, {"title": "Clinical Data Model", "authority": "Epic", "credential_url": null, "issued_at": "Oct 2024"}, {"title": "Clarity Data Model", "authority": "Epic", "credential_url": null, "issued_at": "Sep 2024"}, {"title": "Caboodle Data Model", "authority": "Epic", "credential_url": null, "issued_at": "Jul 2024"}, {"title": "Cogito", "authority": "Epic", "credential_url": null, "issued_at": "May 2024"}], "publications": [], "educations": [{"school": "<PERSON><PERSON> Reactor", "date": {"start": "May 2023", "end": "Sep 2023"}, "degree": "Software Engineering", "description": "Skills: React.js · HTML · PostgreSQL · Python (Programming Language) · JavaScript · Cascading Style Sheets (CSS)", "skills": ["React.js", "HTML", "PostgreSQL", "Python (Programming Language)", "JavaScript", "Cascading Style Sheets (CSS)"]}, {"school": "Case Western Reserve University", "date": {"start": "May 2022"}, "degree": "Master, Physiology, General"}, {"school": "Cleveland State University", "date": {"start": "May 2019"}, "degree": "Bachelor's degree, Health Sciences and Minor in Biology"}], "honors": [], "volunteers": []}, {"id": "962432364", "urn": "ACoAADldjWwBH6fWAXc7ltnJ2PNwwMROxbB4vXY", "public_identifier": "vib<PERSON>-tan<PERSON>a", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "full_name": "<PERSON><PERSON><PERSON>", "headline": "SSCBS’25 | BMS | Crisil Ltd. | Grandeur- The Knowledge and Consulting Cell of SSCBS | Communiqué- The PR Cell of SSCBS | CUET - 98.91 %ile |", "is_premium": false, "is_open_to_work": true, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1641814195001, "created_date": "2022-01-10T11:29:55.001Z", "location": {"country": "India", "country_code": "IN", "city": "Faridabad, Haryana, India", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D4D03AQFaqWvl07yhgg/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/1693678349182?e=**********&v=beta&t=nqkSwM-2X6z61tJBksOoDiTIahCCM3-IpHLgwPEX5ec", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D03AQFaqWvl07yhgg/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1693678349182?e=**********&v=beta&t=a7PJgoWNeEiU0w9zLWeVY9yJwq_Uxnlua7GiIhctAuA", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D4D03AQFaqWvl07yhgg/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1693678349182?e=**********&v=beta&t=L8GALNA0xSb03NggkkNWYA9rXF0FyAc4mYc-WTRRlOQ", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D4D03AQFaqWvl07yhgg/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1693678349182?e=**********&v=beta&t=CNQEo-o2ISuv5Wuzz3KRLhPFOX8GY6rPMgcOghtZ-LQ", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Vice President", "date": {"start": "Jun 2024", "end": "Present"}, "company": {"id": "14594308", "name": "Comm<PERSON><PERSON><PERSON> - The Public Relations Cell of SSCBS", "url": "https://www.linkedin.com/company/14594308/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGYrvhbmJq6hA/company-logo_200_200/company-logo_200_200/0/1630548297304/communique_the_public_relations_cell_of_sscbs_logo?e=**********&v=beta&t=5TokIAaALu3quSYhfGFWBV0N9Uke2RqWNo5wmdbjjP4", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGYrvhbmJq6hA/company-logo_100_100/company-logo_100_100/0/1630548297304/communique_the_public_relations_cell_of_sscbs_logo?e=**********&v=beta&t=aqD48vfmj36S6RRa0TOHNVY0egYnqwuwuynQ8YI7Jaw", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGYrvhbmJq6hA/company-logo_400_400/company-logo_400_400/0/1630548297304/communique_the_public_relations_cell_of_sscbs_logo?e=**********&v=beta&t=iN7enheYjC-o8hZiAB9XvJhSqLV_QFr0J2nZl4U7e7M", "expires_at": **********000}]}}, {"title": "Coordinator", "description": "Skills: Negotiation · Teamwork", "date": {"start": "Jul 2023", "end": "Jun 2024"}, "company": {"id": "14594308", "name": "Comm<PERSON><PERSON><PERSON> - The Public Relations Cell of SSCBS", "url": "https://www.linkedin.com/company/14594308/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGYrvhbmJq6hA/company-logo_200_200/company-logo_200_200/0/1630548297304/communique_the_public_relations_cell_of_sscbs_logo?e=**********&v=beta&t=5TokIAaALu3quSYhfGFWBV0N9Uke2RqWNo5wmdbjjP4", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGYrvhbmJq6hA/company-logo_100_100/company-logo_100_100/0/1630548297304/communique_the_public_relations_cell_of_sscbs_logo?e=**********&v=beta&t=aqD48vfmj36S6RRa0TOHNVY0egYnqwuwuynQ8YI7Jaw", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGYrvhbmJq6hA/company-logo_400_400/company-logo_400_400/0/1630548297304/communique_the_public_relations_cell_of_sscbs_logo?e=**********&v=beta&t=iN7enheYjC-o8hZiAB9XvJhSqLV_QFr0J2nZl4U7e7M", "expires_at": **********000}]}}, {"title": "Organising Committee Member", "description": "Skills: Negotiation · Teamwork", "date": {"start": "Jan 2023", "end": "Jul 2023"}, "company": {"id": "14594308", "name": "Comm<PERSON><PERSON><PERSON> - The Public Relations Cell of SSCBS", "url": "https://www.linkedin.com/company/14594308/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGYrvhbmJq6hA/company-logo_200_200/company-logo_200_200/0/1630548297304/communique_the_public_relations_cell_of_sscbs_logo?e=**********&v=beta&t=5TokIAaALu3quSYhfGFWBV0N9Uke2RqWNo5wmdbjjP4", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGYrvhbmJq6hA/company-logo_100_100/company-logo_100_100/0/1630548297304/communique_the_public_relations_cell_of_sscbs_logo?e=**********&v=beta&t=aqD48vfmj36S6RRa0TOHNVY0egYnqwuwuynQ8YI7Jaw", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGYrvhbmJq6hA/company-logo_400_400/company-logo_400_400/0/1630548297304/communique_the_public_relations_cell_of_sscbs_logo?e=**********&v=beta&t=iN7enheYjC-o8hZiAB9XvJhSqLV_QFr0J2nZl4U7e7M", "expires_at": **********000}]}}, {"title": "Summer Intern", "location": "Gurugram, Haryana, India", "date": {"start": "Jun 2024", "end": "Present"}, "employment_type": "Full-time", "company": {"id": "163261", "name": "CRISIL Limited", "url": "https://www.linkedin.com/company/163261/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQG_zHjUSVs4Cw/company-logo_200_200/company-logo_200_200/0/1736428019116/crisil_logo?e=**********&v=beta&t=NSF-WP_mU19rl44Mw_uSQiRF_D4P2YMcMxit6D6vIwM", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQG_zHjUSVs4Cw/company-logo_100_100/company-logo_100_100/0/1736428019116/crisil_logo?e=**********&v=beta&t=2aFTAO8UR8jxQ6wC-D42y45qEcGvg9mOuEFhp1-awGQ", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQG_zHjUSVs4Cw/company-logo_400_400/company-logo_400_400/0/1736428019116/crisil_logo?e=**********&v=beta&t=BsHcbOn2fRutCZcu6cXK7mFi-JqNGVn4YySN-F-eUnU", "expires_at": **********000}]}}, {"title": "Advisory Committee Member", "date": {"start": "May 2024", "end": "Present"}, "company": {"id": "13652727", "name": "Grandeur - The Consulting and Knowledge Cell of SSCBS", "url": "https://www.linkedin.com/company/13652727/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQEuT6n8yhHdwg/company-logo_200_200/company-logo_200_200/0/1630512581359?e=**********&v=beta&t=NuP1Io-B5r-lzBP6AISIg6F2MBI1Vaxiv2SkliltMCQ", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQEuT6n8yhHdwg/company-logo_100_100/company-logo_100_100/0/1630512581359?e=**********&v=beta&t=sHGvfNzkw16_kywQksWy_iDwUZOVvrUbZwDGqF7ptl0", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQEuT6n8yhHdwg/company-logo_400_400/company-logo_400_400/0/1630512581359?e=**********&v=beta&t=D8XqbcBq9qRW3k-eCvCayd4-DN1Vkk-uMd8-n5GieKU", "expires_at": **********000}]}}, {"title": "Core Committee Member", "description": "Skills: Negotiation · Teamwork", "date": {"start": "Jul 2023", "end": "May 2024"}, "company": {"id": "13652727", "name": "Grandeur - The Consulting and Knowledge Cell of SSCBS", "url": "https://www.linkedin.com/company/13652727/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQEuT6n8yhHdwg/company-logo_200_200/company-logo_200_200/0/1630512581359?e=**********&v=beta&t=NuP1Io-B5r-lzBP6AISIg6F2MBI1Vaxiv2SkliltMCQ", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQEuT6n8yhHdwg/company-logo_100_100/company-logo_100_100/0/1630512581359?e=**********&v=beta&t=sHGvfNzkw16_kywQksWy_iDwUZOVvrUbZwDGqF7ptl0", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQEuT6n8yhHdwg/company-logo_400_400/company-logo_400_400/0/1630512581359?e=**********&v=beta&t=D8XqbcBq9qRW3k-eCvCayd4-DN1Vkk-uMd8-n5GieKU", "expires_at": **********000}]}}, {"title": "Organising Committee Member", "description": "Skills: Negotiation · Teamwork", "date": {"start": "Jan 2023", "end": "Jul 2023"}, "company": {"id": "13652727", "name": "Grandeur - The Consulting and Knowledge Cell of SSCBS", "url": "https://www.linkedin.com/company/13652727/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQEuT6n8yhHdwg/company-logo_200_200/company-logo_200_200/0/1630512581359?e=**********&v=beta&t=NuP1Io-B5r-lzBP6AISIg6F2MBI1Vaxiv2SkliltMCQ", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQEuT6n8yhHdwg/company-logo_100_100/company-logo_100_100/0/1630512581359?e=**********&v=beta&t=sHGvfNzkw16_kywQksWy_iDwUZOVvrUbZwDGqF7ptl0", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQEuT6n8yhHdwg/company-logo_400_400/company-logo_400_400/0/1630512581359?e=**********&v=beta&t=D8XqbcBq9qRW3k-eCvCayd4-DN1Vkk-uMd8-n5GieKU", "expires_at": **********000}]}}, {"title": "Student", "description": "Skills: Finance · Teamwork", "location": "India", "date": {"start": "Nov 2022", "end": "Present"}, "company": {"id": "15139428", "name": "<PERSON><PERSON> College Of Business Studies", "url": "https://www.linkedin.com/company/15139428/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C510BAQFcmAg3z9OUhg/company-logo_200_200/company-logo_200_200/0/1631425420701/shaheed_sukhdev_college_of_business_studies_logo?e=**********&v=beta&t=5ggHMRU_DMjQujl8eHYYh6h81vNSBf8123IaFqAO_rE", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C510BAQFcmAg3z9OUhg/company-logo_100_100/company-logo_100_100/0/1631425420701/shaheed_sukhdev_college_of_business_studies_logo?e=**********&v=beta&t=j7ewbBf-e6ZKOhtA-L9hN0g5UbzLKZOfvueeDbsbH4o", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C510BAQFcmAg3z9OUhg/company-logo_400_400/company-logo_400_400/0/1631425420701/shaheed_sukhdev_college_of_business_studies_logo?e=**********&v=beta&t=5NAKZZ6zdPvnwM4mSU4BBYvPk6oirurKgjm1deJ_5sU", "expires_at": **********000}]}}, {"title": "Research Intern", "description": "Skills: Finance", "date": {"start": "Aug 2023", "end": "Sep 2023"}, "company": {"id": "85637058", "name": "Evolve", "url": "https://www.linkedin.com/company/85637058/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQEHm3qg5e9dQg/company-logo_200_200/company-logo_200_200/0/1718270673546/letsevolvein_logo?e=**********&v=beta&t=4B_IWi-Cpb1gmsvn7bH3HMAPE5ZqN3PJz0p8jx_fk08", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQEHm3qg5e9dQg/company-logo_100_100/company-logo_100_100/0/1718270673546/letsevolvein_logo?e=**********&v=beta&t=T2Bkysnk9SHBI8-AQQHCPfFtbfsnYxL9GTRPGrvI7ac", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D4D0BAQEHm3qg5e9dQg/company-logo_400_400/company-logo_400_400/0/1718270673546/letsevolvein_logo?e=**********&v=beta&t=to5daifrXFGmTGegqRsCm7KeIkFkeA5Ib6LAQbvwoZM", "expires_at": **********000}]}}, {"title": "Intern", "location": "India · Remote", "date": {"start": "May 2023", "end": "Jul 2023"}, "employment_type": "Internship", "company": {"id": "74448091", "name": "Skilled Sapiens", "url": "https://www.linkedin.com/company/74448091/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQFzcDqVjIvBPg/company-logo_200_200/company-logo_200_200/0/1630643778587?e=**********&v=beta&t=_sNVTEws8wNnZKsDQkRiUuYGWpu2HleNtBCP21U67pA", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQFzcDqVjIvBPg/company-logo_100_100-alternative/company-logo_100_100-alternative/0/1630643778576?e=**********&v=beta&t=XIdyU77_U_Jx10U2L7hDxJoWS9E9QqJV50fwG0gJkIU", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQFzcDqVjIvBPg/company-logo_400_400-alternative/company-logo_400_400-alternative/0/1630643778576?e=**********&v=beta&t=ZFCp9m8U8-zunCyNU-g32uFMuibiGqlXUhUwwnWwx4A", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQFzcDqVjIvBPg/company-logo_200_200-alternative/company-logo_200_200-alternative/0/1630643778576?e=**********&v=beta&t=2XMrpEaEFtXMMMu4zWKQqG_rUIN-BGAuviiKT57769o", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQFzcDqVjIvBPg/company-logo_100_100/company-logo_100_100/0/1630643778587?e=**********&v=beta&t=dzZiPjXvtOSxgbf2aakXhCObis4Jru3TJ9gmXqgE9L8", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQFzcDqVjIvBPg/company-logo_400_400/company-logo_400_400/0/1630643778587?e=**********&v=beta&t=OxbcquQn7RzRSqQ2h92H0ZwZOhshIXcLIOUSfjRATCk", "expires_at": **********000}]}}, {"title": "Organising Committee Member", "description": "Skills: Negotiation · Teamwork", "date": {"start": "Jan 2023", "end": "Jul 2023"}, "company": {"id": "34221477", "name": "Alumni Relations and Outreach Cell - SSCBS", "url": "https://www.linkedin.com/company/34221477/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQHDru3WTeQvWg/company-logo_200_200/company-logo_200_200/0/1630591988647/aroc_sscbs_logo?e=**********&v=beta&t=S6_S1WZ5D0H8DLpW0wOVmTibsWBTmuY93wqa6QWdfTU", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQHDru3WTeQvWg/company-logo_100_100/company-logo_100_100/0/1630591988647/aroc_sscbs_logo?e=**********&v=beta&t=gKew6OepOJ9_T_N8SePXM0FnBK2d5xrvU8-XJVMw1B4", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQHDru3WTeQvWg/company-logo_400_400/company-logo_400_400/0/1630591988647/aroc_sscbs_logo?e=**********&v=beta&t=ryXdykN2Cu8VWNTcS6kc96AzFAcBiQOviq_MZ-pLAxU", "expires_at": **********000}]}}, {"title": "Fundraising Intern", "location": "Remote", "date": {"start": "Jan 2023", "end": "Feb 2023"}, "employment_type": "Part-time", "company": {"id": "66679640", "name": "Pledge A Smile", "url": "https://www.linkedin.com/company/66679640/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C560BAQGh9eShkcScSg/company-logo_200_200/company-logo_200_200/0/1630642962813?e=**********&v=beta&t=ZNnplqXjXgxFzfApShN4SYd2nFdlFdhf3K7kL_lhnRg", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C560BAQGh9eShkcScSg/company-logo_100_100/company-logo_100_100/0/1630642962813?e=**********&v=beta&t=QtrGd5lv0It0t-fJXu2kk2FI4Ap85Tlfx6sTsNh5DOk", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C560BAQGh9eShkcScSg/company-logo_400_400/company-logo_400_400/0/1630642962813?e=**********&v=beta&t=2UIPGjMT0nmslBLEfV1Hv34gdl9dVZDeBA8Os_RSSfw", "expires_at": **********000}]}}], "skills": [{"skill": "Critical Thinking"}, {"skill": "Negotiation"}, {"skill": "Teamwork"}, {"skill": "Finance"}, {"skill": "Financial Modeling"}, {"skill": "Marketing"}], "certifications": [{"title": "NISM XV - Research Analyst", "authority": "National Institute of Securities Markets (NISM)", "credential_url": "https://drive.google.com/file/d/1Rj5kXxbqPPdNNncvHFD3fHtCzoP1i5AL/view?usp=sharing", "issued_at": "Jun 2024"}, {"title": "Business and Financial Modelling", "authority": "Coursera", "credential_url": "https://drive.google.com/file/d/1F3pmLqyG5qIiVGjCX5-psAyXx9tQ1q1A/view?usp=sharing", "issued_at": "Sep 2023"}, {"title": "Metvy Finance Program", "authority": "<PERSON><PERSON>", "credential_url": "https://drive.google.com/file/d/1FaR5EJjI76ACf3Nf8FWZXcsrgD72WJHs/view?usp=sharing", "issued_at": "Oct 2022"}, {"title": "Marketing Essentials", "authority": "Harvard Business School Online", "credential_url": "https://drive.google.com/file/d/1_j3VLFzB24xxb9y3ce9iZHLv3UDu7kz1/view?usp=sharing"}], "publications": [], "educations": [{"school": "<PERSON><PERSON> College Of Business Studies", "date": {"start": "Nov 2022", "end": "Jun 2025"}, "degree": "BMS, Business, Management, Marketing, and Related Support Services", "description": "Skills: Critical Thinking · Teamwork", "skills": ["Critical Thinking", "Teamwork"]}, {"school": "Modern Delhi Public School - India", "date": {"start": "Apr 2008", "end": "Jun 2022"}, "degree": "High School Diploma", "description": "Skills: Critical Thinking · Teamwork", "skills": ["Critical Thinking", "Teamwork"]}], "honors": [{"title": "National Winners", "issued_by": "IIM Udaipur", "issued_at": "Nov 2023", "associated_with": "<PERSON><PERSON> College Of Business Studies"}, {"title": "2nd Position ", "issued_by": "Hansraj College, Delhi University ", "associated_with": "<PERSON><PERSON> College Of Business Studies"}, {"title": "3rd Position ", "issued_by": "<PERSON><PERSON> College", "associated_with": "<PERSON><PERSON> College Of Business Studies"}], "volunteers": []}, {"id": "871017960", "urn": "ACoAADPqregBPMkHznO_y8c2AutDCHIxXl5ohwA", "public_identifier": "peddi-lohith-3a0851203", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Lohith", "full_name": "<PERSON><PERSON><PERSON>", "headline": "Student at Chaitanya Bharathi Institute Of Technology", "is_premium": false, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1610287686773, "created_date": "2021-01-10T14:08:06.773Z", "location": {"country": "India", "country_code": "IN", "city": "Hyderabad, Telangana, India", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D4E03AQH87yLaIes2Vg/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/1715779195426?e=**********&v=beta&t=deWH97ZyaSjPkoyLa0R5HdBeR1K8A_JNuKkDeokN_Q0", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D4E03AQH87yLaIes2Vg/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1715779195426?e=**********&v=beta&t=jpKhP__2wOJLZfIZCAtvcQ6Xa1YOMedOHTtrhWhUoeQ", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D4E03AQH87yLaIes2Vg/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1715779195426?e=**********&v=beta&t=JrzCUWP9FCX7ocostzXdKN48KM2i7OOv5Nh_3gIx-JY", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D4E03AQH87yLaIes2Vg/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1715779195426?e=**********&v=beta&t=kaMeXkOGVVSPv4TOkpKN9iNg4083qJvdv63hYx7VbFo", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Open Source Developer", "description": "Code for GovTech (C4GT)  is a unique program aimed at creating India's first active open-source community of coders that can build and contribute to global Digital Public Goods.", "location": "Hyderabad, Telangana, India · Remote", "date": {"start": "Jul 2023", "end": "Aug 2023"}, "employment_type": "Internship", "skills": ["Python (Programming Language)", "HTML", "Django", "Data Visualization", "Cascading Style Sheets (CSS)", "Bootstrap (Framework)"], "company": {"id": "81648334", "name": "Code for GovTech (C4GT)", "url": "https://www.linkedin.com/company/81648334/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGn2b2LkF3pyQ/company-logo_200_200/company-logo_200_200/0/1652419798971/code_for_govtech_logo?e=**********&v=beta&t=R1yXpJ6PJoNlrXUW1lH824eq0AIkGhdcuQsTgZwhyKo", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGn2b2LkF3pyQ/company-logo_100_100/company-logo_100_100/0/1652419798971/code_for_govtech_logo?e=**********&v=beta&t=GN-ivvpTCnm8dNeB_oy4Ouc-PdWnHrmtbh9rEe-H0pw", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGn2b2LkF3pyQ/company-logo_400_400/company-logo_400_400/0/1652419798971/code_for_govtech_logo?e=**********&v=beta&t=wqSJVzW3LRatT_h7G9xvAuXhbqkg4d3QXE82QlZ7SXw", "expires_at": **********000}]}}, {"title": "Student Intern", "location": "India", "date": {"start": "Sep 2021", "end": "Nov 2021"}, "employment_type": "Internship", "company": {"id": "79729104", "name": "GirlScript Winter of Contributing", "url": "https://www.linkedin.com/company/79729104/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C560BAQGElhoksb5zYw/company-logo_200_200/company-logo_200_200/0/1630643121482/girlscript_winter_of_contributing_logo?e=**********&v=beta&t=MDggJFPUywzzy5b81kYbCnj5Cx-mx2WUTPJ2KpHX5uY", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C560BAQGElhoksb5zYw/company-logo_100_100/company-logo_100_100/0/1630643121482/girlscript_winter_of_contributing_logo?e=**********&v=beta&t=qcoKuKAw6Az6ZJ6N2_49Zpb29JlyDM-n7L_rzvGd0RU", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C560BAQGElhoksb5zYw/company-logo_400_400/company-logo_400_400/0/1630643121482/girlscript_winter_of_contributing_logo?e=**********&v=beta&t=HSbImAFwZcxRQxZvuaCG_OxNPqQd7tS66uAv4jaIIf8", "expires_at": **********000}]}}], "skills": [{"skill": "Hyperledger"}, {"skill": "Truffle Framework"}, {"skill": "Python (Programming Language)"}, {"skill": "Django"}, {"skill": "Data Visualization"}, {"skill": "HTML"}, {"skill": "Cascading Style Sheets (CSS)"}, {"skill": "Bootstrap (Framework)"}, {"skill": "React.js"}], "certifications": [{"title": "React.js Essential Training", "authority": "LinkedIn", "credential_url": "https://www.linkedin.com/learning/certificates/ef7192c9d0895810094b27bf7ad7e1ca7c7a18d80b89f7eb95aff3fb1d29f5b1", "issued_at": "Apr 2023"}], "publications": [], "educations": [{"school": "Chaitanya Bharathi Institute Of Technology", "date": {"start": "2020", "end": "2024"}, "degree": "Bachelor of Engineering - BE, Computer Science"}, {"school": "Chinmaya Vidyalaya", "date": {"start": "Apr 2007", "end": "Apr 2018"}}], "honors": [], "volunteers": []}, {"id": "839823681", "urn": "ACoAADIOsUEBK0sPsLdPqDj3SMAOLlWXwvX-LeU", "public_identifier": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON><PERSON>", "full_name": "<PERSON><PERSON>", "headline": "BDR @ Fireblocks - Payments | GTM, Sales, BD", "is_premium": true, "is_open_to_work": false, "is_hiring": false, "is_memorialized": false, "is_influencer": false, "is_top_voice": false, "is_creator": false, "birth": {"day": null, "month": null, "year": null}, "pronoun": null, "created": 1598782208077, "created_date": "2020-08-30T10:10:08.077Z", "location": {"country_code": "SG", "city": "Singapore", "postal_code": null}, "avatar": [{"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D5603AQG8kMK1SFxn6A/profile-displayphoto-shrink_100_100/profile-displayphoto-shrink_100_100/0/1718235215563?e=**********&v=beta&t=0MQq00UPIgJvmjXOQ-GjC0zoxEfdVJd9wr3SPqy_d7c", "expires_at": **********000}, {"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5603AQG8kMK1SFxn6A/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1718231523784?e=**********&v=beta&t=3WkJKSJRNnco7MDACTuBpDyHHUlJoypx3HClvB18gZ8", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D5603AQG8kMK1SFxn6A/profile-displayphoto-shrink_400_400/profile-displayphoto-shrink_400_400/0/1718228032100?e=**********&v=beta&t=Vn5YvpU5fpF6YPmGYThYGFgAszAKKk3Ni4Fz10ywVDw", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/v2/D5603AQG8kMK1SFxn6A/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1718232724279?e=**********&v=beta&t=R6freBTZpy-DcPr8JiVcvI0Ak7OklE-GjpDSL0dgljE", "expires_at": **********000}], "cover": [{"width": 1400, "height": 350, "url": "https://media.licdn.com/dms/image/v2/D5616AQGe7PTsRHG-3Q/profile-displaybackgroundimage-shrink_350_1400/B56ZUjAiq1GQAY-/0/1740049102913?e=**********&v=beta&t=PbsJI08NVT5DyCxkbMiZvOlNggQfWSTtJ3Br95t5vqw", "expires_at": **********000}, {"width": 800, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D5616AQGe7PTsRHG-3Q/profile-displaybackgroundimage-shrink_200_800/B56ZUjAiq1GQAU-/0/1740049102913?e=**********&v=beta&t=-kC9DkVhk5reQGQFxmq0MUrEuotjtXRMUkCAbQ4tfnE", "expires_at": **********000}], "website": {}, "supported_locales": [{"country": "US", "language": "en"}], "primary_locale": {"country": "US", "language": "en"}, "experiences": [{"title": "Business Development Representative", "description": "Fireblocks is an easy-to-use platform to manage day-to-day digital asset operations and create new blockchain based products. We're a trusted technology partner for 2000+ institutions in the financial, payments, and web3 space and has secured the transfer of over $6 trillion in digital assets.\n\nTo initiate a tailored discussion around your specific use case, feel free to <NAME_EMAIL>.", "date": {"start": "Aug 2024", "end": "Present"}, "employment_type": "Full-time", "company": {"id": "14824547", "name": "Fireblocks", "url": "https://www.linkedin.com/company/14824547/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQEyT6gpuwTpPg/company-logo_200_200/company-logo_200_200/0/1630561416766/fireblocks_logo?e=**********&v=beta&t=I1j_d4u5c3EpVQIlFdgRCjjdM6Vo2--QjUSomzJsGLM", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQEyT6gpuwTpPg/company-logo_100_100/company-logo_100_100/0/1630561416766/fireblocks_logo?e=**********&v=beta&t=yrc-clAdaAFXbUmrFUdFmIFNrB11gnQnc1HRS7EQdyY", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQEyT6gpuwTpPg/company-logo_400_400/company-logo_400_400/0/1630561416766/fireblocks_logo?e=**********&v=beta&t=NSJFdUY0jLYvp_w6Lo27yBFcyutHO5pbvPQKMJ_aghY", "expires_at": **********000}]}}, {"title": "Maritime Commercial", "description": "Developed strategies and financial projection for new/renewed vessel contracts and decarbonization", "date": {"start": "Aug 2023", "end": "Dec 2023"}, "employment_type": "Internship", "company": {"id": "1271", "name": "Shell", "url": "https://www.linkedin.com/company/1271/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGN30g7aSl4NA/company-logo_200_200/company-logo_200_200/0/1631303787196?e=**********&v=beta&t=G5LFL_AwYVrWvUaOIstURfjKb2fJSdv_lH-QmOgkxlk", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGN30g7aSl4NA/company-logo_100_100/company-logo_100_100/0/1631303787196?e=**********&v=beta&t=F-Mrwm4Q0Nth5HMx5tp-jo03P1g838ZA7i-4CV16uRI", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQGN30g7aSl4NA/company-logo_400_400/company-logo_400_400/0/1631303787196?e=**********&v=beta&t=VTHyOY5yGeI73c2Do1WaLuMVCKRyz_wm0l9_q2EXi4Q", "expires_at": **********000}]}, "media": [{"description": null, "thumbnail": [{"width": 2048, "height": 1536, "url": "https://media.licdn.com/dms/image/sync/v2/D5622AQG2nRl92WR87w/feedshare-shrink_2048_1536/feedshare-shrink_2048_1536/0/1707745758000?e=1752105600&v=beta&t=7LIWkhLW3fsTYOXsELsJBhBkePPZCBKssx-x4SCT9HE", "expires_at": 1752105600000}, {"width": 20, "height": 15, "url": "https://media.licdn.com/dms/image/sync/v2/D5622AQG2nRl92WR87w/feedshare-shrink_20/feedshare-shrink_20/0/1707745746949?e=1752105600&v=beta&t=wY9YDfAw_vpEdmnwzHf0iPsOoGP3WKAUZFroFfaqZkw", "expires_at": 1752105600000}, {"width": 1280, "height": 960, "url": "https://media.licdn.com/dms/image/sync/v2/D5622AQG2nRl92WR87w/feedshare-shrink_1280/feedshare-shrink_1280/0/1707745746949?e=1752105600&v=beta&t=o0zF4C2ks6AO3ogR76gHAwLnhZk2sqopHhPcTNrcDoo", "expires_at": 1752105600000}, {"width": 480, "height": 360, "url": "https://media.licdn.com/dms/image/sync/v2/D5622AQG2nRl92WR87w/feedshare-shrink_480/feedshare-shrink_480/0/1707745746949?e=1752105600&v=beta&t=mo2WYblUZ-lfMQVBUb0eDHaY0Y-zsunzPjhQ_nSTW5M", "expires_at": 1752105600000}, {"width": 160, "height": 120, "url": "https://media.licdn.com/dms/image/sync/v2/D5622AQG2nRl92WR87w/feedshare-shrink_160/feedshare-shrink_160/0/1707745746949?e=1752105600&v=beta&t=TxZFAOa2uNy86yr11cfSN_ZG5RKayy_UhRasn4r2MYk", "expires_at": 1752105600000}, {"width": 800, "height": 600, "url": "https://media.licdn.com/dms/image/sync/v2/D5622AQG2nRl92WR87w/feedshare-shrink_800/feedshare-shrink_800/0/1707745746949?e=1752105600&v=beta&t=dzSWlrx4jZ1ASaAm8guvgKoU7l4diFzrolIaBWiSL94", "expires_at": 1752105600000}], "title": "Internship Reflection at Shell"}]}, {"title": "Public Affairs Project", "description": "Helped to launch Temasek Review 2023 via digital media and social platforms", "date": {"start": "May 2023", "end": "Aug 2023"}, "employment_type": "Internship", "company": {"id": "15956", "name": "<PERSON><PERSON><PERSON>", "url": "https://www.linkedin.com/company/15956/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/D560BAQGMY-4ctbsMlg/company-logo_200_200/company-logo_200_200/0/1736232784383/temasek_holdings_logo?e=**********&v=beta&t=tDwF0kImsnzll6taJ0otCf8a9zWCH33_wCMkRWlFfUc", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/D560BAQGMY-4ctbsMlg/company-logo_100_100/company-logo_100_100/0/1736232784383/temasek_holdings_logo?e=**********&v=beta&t=ovab1mEYxR1Mb6oDWY-iWlL7q3G_WlDlEGYRPYDnFEI", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/D560BAQGMY-4ctbsMlg/company-logo_400_400/company-logo_400_400/0/1736232784383/temasek_holdings_logo?e=**********&v=beta&t=a6Tay8En--7W6lRipFNO93OhtuGAnubME_VKexsrpus", "expires_at": **********000}]}, "media": [{"description": null, "thumbnail": [{"width": 1190, "height": 520, "url": "https://media.licdn.com/dms/image/sync/v2/D5610AQEVGPRNAN2Nfg/image-shrink_1280/image-shrink_1280/0/1691575501562?e=**********&v=beta&t=CwW8i1JxnK67KwOi8EjFbdKIBUHf_1acSl-b3PohFVI", "expires_at": **********000}, {"width": 480, "height": 209, "url": "https://media.licdn.com/dms/image/sync/v2/D5610AQEVGPRNAN2Nfg/image-shrink_480/image-shrink_480/0/1691575501562?e=**********&v=beta&t=c1wJ6TjIo3owQd-IP4qwnpz1V100a5U3fnRK9N6T36s", "expires_at": **********000}, {"width": 160, "height": 69, "url": "https://media.licdn.com/dms/image/sync/v2/D5610AQEVGPRNAN2Nfg/image-shrink_160/image-shrink_160/0/1691575501562?e=**********&v=beta&t=DI_RVC4Aa83dGOUOVAedj3PP1wpYw8P6FyFQC2glkWk", "expires_at": **********000}, {"width": 20, "height": 8, "url": "https://media.licdn.com/dms/image/sync/v2/D5610AQEVGPRNAN2Nfg/image-shrink_20/image-shrink_20/0/1691575501562?e=**********&v=beta&t=PsR5Vc0TNNc6FvWHPQEeiVNvHVqvJnOwaGyQ0HYGgAs", "expires_at": **********000}, {"width": 800, "height": 349, "url": "https://media.licdn.com/dms/image/sync/v2/D5610AQEVGPRNAN2Nfg/image-shrink_800/image-shrink_800/0/1691575501562?e=**********&v=beta&t=honjRMk9bA7dtUaZFNt-5TAbX7WVPxCMmYO3936XQXU", "expires_at": **********000}], "title": "Sustainability Content | Example post on LinkedIn"}, {"description": null, "thumbnail": [{"width": 1200, "height": 630, "url": "https://media.licdn.com/dms/image/sync/v2/D5610AQGD9Wtsw_ChGQ/image-shrink_1280/image-shrink_1280/0/1689063301931?e=**********&v=beta&t=UiPdDxhzkTYsoePDuxW67Dh5jwR6KxhkRZg-plNDHtg", "expires_at": **********000}, {"width": 480, "height": 252, "url": "https://media.licdn.com/dms/image/sync/v2/D5610AQGD9Wtsw_ChGQ/image-shrink_480/image-shrink_480/0/1689063301931?e=**********&v=beta&t=XWcDq6hacd_4RAqX9x2ORXYjO39FyDrjqYyMISNt6BY", "expires_at": **********000}, {"width": 20, "height": 11, "url": "https://media.licdn.com/dms/image/sync/v2/D5610AQGD9Wtsw_ChGQ/image-shrink_20/image-shrink_20/0/1689063301931?e=**********&v=beta&t=Ivo5SA5G5u9MEO1n8WJ5LWjrCaQVjEzbX-58U1lEiCg", "expires_at": **********000}, {"width": 160, "height": 84, "url": "https://media.licdn.com/dms/image/sync/v2/D5610AQGD9Wtsw_ChGQ/image-shrink_160/image-shrink_160/0/1689063301931?e=**********&v=beta&t=qsrt7BikW2wwGe-RFk9Xpn6KZFftfVW2Cq19RuQrTQI", "expires_at": **********000}, {"width": 300, "height": 157, "url": "https://media.licdn.com/dms/image/sync/v2/D5610AQGD9Wtsw_ChGQ/image_157_300/image_157_300/0/1689066120585?e=**********&v=beta&t=n2McmZ3O_7EcQI_61LkwMXnpHzBxKCo8AuGg9SLeEjE", "expires_at": **********000}, {"width": 800, "height": 420, "url": "https://media.licdn.com/dms/image/sync/v2/D5610AQGD9Wtsw_ChGQ/image-shrink_800/image-shrink_800/0/1689063301931?e=**********&v=beta&t=jFboB3ko3Og1UWujJZAT0RlCRE5aX5BLGXSY14bVzeU", "expires_at": **********000}, {"width": 1200, "height": 627, "url": "https://media.licdn.com/dms/image/sync/v2/D5610AQGD9Wtsw_ChGQ/image_627_1200/image_627_1200/0/1689066120585?e=**********&v=beta&t=dK8NT_Ka0KiPhy2lyL81cinAJ4mcAtcG_GdgLRI5xsI", "expires_at": **********000}], "title": "Launch Announcement of Temasek Review 2023 on LinkedIn"}]}, {"title": "Marketing & Sales", "description": "- Project managed 3 'Think with Google' events \n- Proposed pipeline for 2023 special Google Doodles for Vietnam", "location": "Singapore", "date": {"start": "May 2022", "end": "Aug 2022"}, "employment_type": "Internship", "company": {"id": "1441", "name": "Google", "url": "https://www.linkedin.com/company/1441/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQHiNSL4Or29cg/company-logo_200_200/company-logo_200_200/0/1631311446380?e=**********&v=beta&t=kDuZWjpcb6xRipAAaaZ-trwSP3vuqd_WwJYOuKvqWno", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQHiNSL4Or29cg/company-logo_100_100/company-logo_100_100/0/1631311446380?e=**********&v=beta&t=P7fRMXcaZOep8RSFO2FvaDG-QZ3T86wM8K7N0bSRXLk", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4D0BAQHiNSL4Or29cg/company-logo_400_400/company-logo_400_400/0/1631311446380?e=**********&v=beta&t=_1vYkRU-dUbCois4biXmERX1UCi62sBxn7hcgVnvOm0", "expires_at": **********000}]}, "media": [{"description": null, "thumbnail": [{"width": 800, "height": 457, "url": "https://media.licdn.com/dms/image/sync/v2/C5627AQFx8GYHnO6nLQ/articleshare-shrink_800/articleshare-shrink_800/0/1712211936206?e=**********&v=beta&t=2zH9Hapk8OTrhoYtu5giYmLXwbBIL0g3ErRGP8F027w", "expires_at": **********000}, {"width": 1280, "height": 731, "url": "https://media.licdn.com/dms/image/sync/v2/C5627AQFx8GYHnO6nLQ/articleshare-shrink_1280_800/articleshare-shrink_1280_800/0/1712211936206?e=**********&v=beta&t=D7WfN0YIyVlGRNytTHFK9GMl9jtQ4ltygywVHY2SNzc", "expires_at": **********000}, {"width": 160, "height": 91, "url": "https://media.licdn.com/dms/image/sync/v2/C5627AQFx8GYHnO6nLQ/articleshare-shrink_160/articleshare-shrink_160/0/1712211936206?e=**********&v=beta&t=PbWuIxgPUVsAhT3iI6yGt8QRFd8sh0nCy4MbLRDpUaI", "expires_at": **********000}, {"width": 480, "height": 274, "url": "https://media.licdn.com/dms/image/sync/v2/C5627AQFx8GYHnO6nLQ/articleshare-shrink_480/articleshare-shrink_480/0/1712211936206?e=**********&v=beta&t=cl-c_4qklQcA7021ZbegdUVoO6C5SoYg_eOYhPXNjv8", "expires_at": **********000}], "title": "Referral/Recommendation"}, {"description": "I was nominated and selected to be featured on Google's Keyword Blog on International Intern Day 2022", "thumbnail": [{"width": 800, "height": 332, "url": "https://media.licdn.com/dms/image/sync/v2/D4E27AQEm8KRT5CdfEw/articleshare-shrink_800/articleshare-shrink_800/0/1712154426993?e=**********&v=beta&t=lqnH-_kt5imk4xyzMQtMekf7BHY3UVp5au8o0i9-AUc", "expires_at": **********000}, {"width": 1280, "height": 532, "url": "https://media.licdn.com/dms/image/sync/v2/D4E27AQEm8KRT5CdfEw/articleshare-shrink_1280_800/articleshare-shrink_1280_800/0/1712154426993?e=**********&v=beta&t=h0XJjeIgqCOnooBoQnKWB81MOzlgc2xymHXiWfMyL3o", "expires_at": **********000}, {"width": 160, "height": 66, "url": "https://media.licdn.com/dms/image/sync/v2/D4E27AQEm8KRT5CdfEw/articleshare-shrink_160/articleshare-shrink_160/0/1712154426993?e=**********&v=beta&t=3jw27Ikd0kUFHo1961GHuVB1sPFRwKR9G3IjnlohzGI", "expires_at": **********000}, {"width": 480, "height": 199, "url": "https://media.licdn.com/dms/image/sync/v2/D4E27AQEm8KRT5CdfEw/articleshare-shrink_480/articleshare-shrink_480/0/1712154426993?e=**********&v=beta&t=WAry04YDljPr5jndnApCsp3QPYf165c-uroj0I9L950", "expires_at": **********000}], "title": "What it's like to have a hybrid internship at Google"}, {"description": "Narrative for this recap video was developed by myself", "thumbnail": [{"width": 800, "height": 450, "url": "https://media.licdn.com/dms/image/sync/v2/C5627AQFjHp_iavKumw/articleshare-shrink_800/articleshare-shrink_800/0/1712211936465?e=**********&v=beta&t=Rr9czHUBY6rkd-0fXVw6Nt-PYrBDPyEsmvea6iGo-3U", "expires_at": **********000}, {"width": 1280, "height": 720, "url": "https://media.licdn.com/dms/image/sync/v2/C5627AQFjHp_iavKumw/articleshare-shrink_1280_800/articleshare-shrink_1280_800/0/1712211936465?e=**********&v=beta&t=dEqDmc8Jcts4mSVwHQlGOk8khnCjsHp0qLcR12An9bM", "expires_at": **********000}, {"width": 160, "height": 90, "url": "https://media.licdn.com/dms/image/sync/v2/C5627AQFjHp_iavKumw/articleshare-shrink_160/articleshare-shrink_160/0/1712211936465?e=**********&v=beta&t=55gUO_AimHrwKlUev0o3Nv7Pewaj21PUCpxC23zjre8", "expires_at": **********000}, {"width": 480, "height": 270, "url": "https://media.licdn.com/dms/image/sync/v2/C5627AQFjHp_iavKumw/articleshare-shrink_480/articleshare-shrink_480/0/1712211936465?e=**********&v=beta&t=4cEfZO9EhXgCRA-6y0tFOmdw2GvzNcda1IcLu2Ga5qk", "expires_at": **********000}], "title": "Think Games Vietnam 2022"}]}, {"title": "Projects Management & Strategy Lead", "description": "Created brand awareness and built community interest for the company and blockchain technology with educational content (blogs & podcasts)", "location": "China", "date": {"start": "May 2021", "end": "Mar 2022"}, "employment_type": "Internship", "company": {"id": "13747869", "name": "Mask Network (We're hiring)!", "url": "https://www.linkedin.com/company/13747869/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C560BAQF5BuyESU7tQw/company-logo_200_200/company-logo_200_200/0/1630666674339/masknetwork_logo?e=**********&v=beta&t=a7uLxwe9kJo97oTGfDiTKa0J-plXOiEMC6scZLiIzIc", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C560BAQF5BuyESU7tQw/company-logo_100_100/company-logo_100_100/0/1630666674339/masknetwork_logo?e=**********&v=beta&t=uov7OiGErYiSOEWdx-erGX6JN_3emZrOqEgc-7xZSRQ", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C560BAQF5BuyESU7tQw/company-logo_400_400/company-logo_400_400/0/1630666674339/masknetwork_logo?e=**********&v=beta&t=2pBrhICvBXmjqVKrCwCh_bZtKcIac2_92WekXoYD-vE", "expires_at": **********000}]}, "media": [{"description": null, "thumbnail": [{"width": 800, "height": 548, "url": "https://media.licdn.com/dms/image/sync/v2/D4E27AQFhSFezf6_NAw/articleshare-shrink_800/articleshare-shrink_800/0/1748914182714?e=**********&v=beta&t=Xi8fDAeS2QoghKu1_mO8NBRmpuHxAmVzX8vhs66B2R8", "expires_at": **********000}, {"width": 1166, "height": 800, "url": "https://media.licdn.com/dms/image/sync/v2/D4E27AQFhSFezf6_NAw/articleshare-shrink_1280_800/articleshare-shrink_1280_800/0/1748914182714?e=**********&v=beta&t=vCVkOii6vSmZgr29zEX_oFLFKA_NcAHQSiiqCvRfn3k", "expires_at": **********000}, {"width": 160, "height": 109, "url": "https://media.licdn.com/dms/image/sync/v2/D4E27AQFhSFezf6_NAw/articleshare-shrink_160/articleshare-shrink_160/0/1748914182714?e=**********&v=beta&t=wDCaxtFf_gZpBENmHirmdcQeq5k0E-OnB1U5J9GOqfM", "expires_at": **********000}, {"width": 480, "height": 329, "url": "https://media.licdn.com/dms/image/sync/v2/D4E27AQFhSFezf6_NAw/articleshare-shrink_480/articleshare-shrink_480/0/1748914182618?e=**********&v=beta&t=1sYQWk-lIHLtd_SPreLWfCzQJ5Jr3-fe39KIu8oAQ8M", "expires_at": **********000}], "title": "Blockchain 101 — The Basics of Blockchain Technology for Newbies"}, {"description": null, "thumbnail": [{"width": 800, "height": 533, "url": "https://media.licdn.com/dms/image/sync/v2/D4D27AQEas1XM0K1Lgw/articleshare-shrink_800/articleshare-shrink_800/0/1732829675454?e=**********&v=beta&t=I8wLR0GXen6--VGI_kePvlJK2YkIBfJu4j26ddBVonQ", "expires_at": **********000}, {"width": 1200, "height": 800, "url": "https://media.licdn.com/dms/image/sync/v2/D4D27AQEas1XM0K1Lgw/articleshare-shrink_1280_800/articleshare-shrink_1280_800/0/1732829675454?e=**********&v=beta&t=HNhlSSfkg5mfCzLs7uB6v1NVwrWjPIxN9_wxzbpckGQ", "expires_at": **********000}, {"width": 160, "height": 106, "url": "https://media.licdn.com/dms/image/sync/v2/D4D27AQEas1XM0K1Lgw/articleshare-shrink_160/articleshare-shrink_160/0/1732829675454?e=**********&v=beta&t=ziy_O2m41Ulg9MzlQUjxfvLLZx5GpidAOgkRDPsZ1pM", "expires_at": **********000}, {"width": 480, "height": 320, "url": "https://media.licdn.com/dms/image/sync/v2/D4D27AQEas1XM0K1Lgw/articleshare-shrink_480/articleshare-shrink_480/0/1732829675454?e=**********&v=beta&t=EV6hEj09PIGglq1rLBMmCloXygoderEJK1kOdQb_S1Q", "expires_at": **********000}], "title": "Brief on History and Future of Blockchain Technology"}]}, {"title": "Marketing & Client Success", "description": "Built online presence for the company via social media, search engines, and website", "location": "Singapore", "date": {"start": "May 2021", "end": "Aug 2021"}, "employment_type": "Internship", "company": {"id": "72480267", "name": "Xianchao Logistics", "url": "https://www.linkedin.com/company/72480267/", "logo": [{"width": 200, "height": 200, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGtD4i_MbT3AQ/company-logo_200_200/company-logo_200_200/0/1630620600073/xianchao_logistics_logo?e=**********&v=beta&t=raEeLNMpa-Y2uGN86SgH_4KrGQNIp2tTr2JazLmoXpI", "expires_at": **********000}, {"width": 100, "height": 100, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGtD4i_MbT3AQ/company-logo_100_100/company-logo_100_100/0/1630620600073/xianchao_logistics_logo?e=**********&v=beta&t=SeTQdfVzk0t2155dJc7Xc_djETlvM57xYorrseNAoH8", "expires_at": **********000}, {"width": 400, "height": 400, "url": "https://media.licdn.com/dms/image/v2/C4E0BAQGtD4i_MbT3AQ/company-logo_400_400/company-logo_400_400/0/1630620600073/xianchao_logistics_logo?e=**********&v=beta&t=zJ9K2FaAFtzWPJLwaBxyWsGuxBfUNAlGwC7exmEAgbY", "expires_at": **********000}]}, "media": [{"description": null, "thumbnail": [{"width": 800, "height": 278, "url": "https://media.licdn.com/dms/image/sync/v2/D5627AQFkYKYQ0sE_VQ/articleshare-shrink_800/articleshare-shrink_800/0/1737692726939?e=**********&v=beta&t=w_WDJ1YKxRZzUN3GqAAbkty8f8jVEadZMfjxamiWwEw", "expires_at": **********000}, {"width": 1000, "height": 348, "url": "https://media.licdn.com/dms/image/sync/v2/D5627AQFkYKYQ0sE_VQ/articleshare-shrink_1280_800/articleshare-shrink_1280_800/0/1737692726939?e=**********&v=beta&t=HJUl9h_BcgagYI64WZzVV-X9pHeFB7uMLzo7hN32g3o", "expires_at": **********000}, {"width": 160, "height": 55, "url": "https://media.licdn.com/dms/image/sync/v2/D5627AQFkYKYQ0sE_VQ/articleshare-shrink_160/articleshare-shrink_160/0/1737692726913?e=**********&v=beta&t=bBG-W2-e_T5WLyI9i01mfZOEWfulSElzB1adiEg8Ds4", "expires_at": **********000}, {"width": 480, "height": 167, "url": "https://media.licdn.com/dms/image/sync/v2/D5627AQFkYKYQ0sE_VQ/articleshare-shrink_480/articleshare-shrink_480/0/1737692726913?e=**********&v=beta&t=5dQMDLEddDqTjgE1scsoeGmn3qa758z0wTV554O5iGw", "expires_at": **********000}], "title": "Reinvent E-commerce During and After COVID"}, {"description": null, "thumbnail": [{"width": 800, "height": 533, "url": "https://media.licdn.com/dms/image/sync/v2/D5627AQHt-nT37TqpBA/articleshare-shrink_800/articleshare-shrink_800/0/1723056388674?e=**********&v=beta&t=zTdJg2q2YN2ADjHGcpSAVaqTmC0SgUEK2Ay5mBZYVNA", "expires_at": **********000}, {"width": 1000, "height": 667, "url": "https://media.licdn.com/dms/image/sync/v2/D5627AQHt-nT37TqpBA/articleshare-shrink_1280_800/articleshare-shrink_1280_800/0/1723056388674?e=**********&v=beta&t=OmT7UIe5O9KftqXGj1CEZhZgjNJMC2fifSwKL7S4WWY", "expires_at": **********000}, {"width": 160, "height": 106, "url": "https://media.licdn.com/dms/image/sync/v2/D5627AQHt-nT37TqpBA/articleshare-shrink_160/articleshare-shrink_160/0/1723056388675?e=**********&v=beta&t=6O01Iy-LdRQLGiDVHjXVfY_ZtO8yUvxJH0opyEZwOj0", "expires_at": **********000}, {"width": 480, "height": 320, "url": "https://media.licdn.com/dms/image/sync/v2/D5627AQHt-nT37TqpBA/articleshare-shrink_480/articleshare-shrink_480/0/1723056388675?e=**********&v=beta&t=ilPRLehaXzvGOaSypC7DbiE8owu8R4f8aN_p3OApYug", "expires_at": **********000}], "title": "Avoid Bottlenecks in Dropshipping"}]}], "skills": [{"skill": "Python (Programming Language)"}, {"skill": "Adobe Photoshop", "num_endorsements": 1, "is_passed_skill_assessment": true}, {"skill": "Microsoft Office", "num_endorsements": 1}, {"skill": "Google Suite", "num_endorsements": 1}, {"skill": "JavaScript", "num_endorsements": 1}], "certifications": [{"title": "Python Beginner Course", "authority": "University of St.Gallen", "credential_url": null, "issued_at": "May 2023"}, {"title": "Coding Fundamentals", "authority": "<PERSON><PERSON><PERSON>, Google", "credential_url": null, "issued_at": "Jan 2021"}], "publications": [], "educations": [{"school": "Singapore Management University", "date": {"start": "2020"}, "degree": "Bachelor of Business Administration - BBA", "description": "Honors/Awards:\n- SMU International Scholarship \n- SMU Excellence in Student Life 2023: President’s Award, Gold Award in Special Interest, and Bronze Award in School/Office/Centre/Institute\n- <PERSON> Outstanding Intern of the Year Award 2022 \n- SMU Students' Association Student Life Award 2021 - 2022\n- SMU Excellence in Student Life 2022 Bronze Award in Special Interest\n- LKCSB Dean's List AY 2020/2021, 2021/2022\n- Student Council of Discipline’s CIRCLE Award in Responsibility 2021\n- Special Prize & Korea Economic Daily CEO Award (Asian Students' Venture Forum 2022)\n\nCo-curricular Activities:\n- TEDxSMU 2023 - The Art of Play | Executive Producer (2023)\n- SMU Chao Vietnam | Marketing Director (2021) - President (2022)\n- SMU Verts | Events Director (2021) - President (2022)\n- SMU Blockchain | Head of Marketing & Communications (2022)\n- SMU International Connections (ICON) | 10th Management Committee (2022)\n- SMU Mixed Martial Arts | Member (2023-2024)", "media": [{"description": null, "thumbnail": [{"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/sync/v2/C5627AQF_ppzaLjCiyA/articleshare-shrink_800/articleshare-shrink_800/0/1717090099796?e=**********&v=beta&t=-7rdb6r9RKMh1pqgZLZv__4afd9W7UUjK3Zihpe8ck8", "expires_at": **********000}, {"width": 800, "height": 800, "url": "https://media.licdn.com/dms/image/sync/v2/C5627AQF_ppzaLjCiyA/articleshare-shrink_1280_800/articleshare-shrink_1280_800/0/1717090099797?e=**********&v=beta&t=U1ctkdy4_iznvY-EpQpOnB6xO1GsSRpmCidAPd8SG_c", "expires_at": **********000}, {"width": 160, "height": 160, "url": "https://media.licdn.com/dms/image/sync/v2/C5627AQF_ppzaLjCiyA/articleshare-shrink_160/articleshare-shrink_160/0/1717090099797?e=**********&v=beta&t=ve3Qtvdtahi9hAygWjpXQPktktELmRLAHeKWskQS_L8", "expires_at": **********000}, {"width": 480, "height": 480, "url": "https://media.licdn.com/dms/image/sync/v2/C5627AQF_ppzaLjCiyA/articleshare-shrink_480/articleshare-shrink_480/0/1717090099797?e=**********&v=beta&t=Wve7AuSGfmdQzjtfIgvG64gd-pRq4jgLCcIOLeE8MAU", "expires_at": **********000}], "title": "SMU Verts' Executive Committee of 2022"}, {"description": null, "thumbnail": [{"width": 800, "height": 1000, "url": "https://media.licdn.com/dms/image/sync/v2/D5627AQHyFwoxEnnu1g/articleshare-shrink_800/articleshare-shrink_800/0/1734047394441?e=**********&v=beta&t=MlUmDub9mSM0g3i7_QarKg7_gLlxFOUSCsxuy9lUKBo", "expires_at": **********000}, {"width": 640, "height": 800, "url": "https://media.licdn.com/dms/image/sync/v2/D5627AQHyFwoxEnnu1g/articleshare-shrink_1280_800/articleshare-shrink_1280_800/0/1734047394441?e=**********&v=beta&t=yHkjJOmw2KgOP4T8wWRnOYR8-5e8X5yV6PzPIMNxgmo", "expires_at": **********000}, {"width": 160, "height": 200, "url": "https://media.licdn.com/dms/image/sync/v2/D5627AQHyFwoxEnnu1g/articleshare-shrink_160/articleshare-shrink_160/0/1734047394441?e=**********&v=beta&t=_jVlIdrLR_OrGyoAg-x-9svRcORI70qR7ssiJrIwnq4", "expires_at": **********000}, {"width": 480, "height": 600, "url": "https://media.licdn.com/dms/image/sync/v2/D5627AQHyFwoxEnnu1g/articleshare-shrink_480/articleshare-shrink_480/0/1734047394441?e=**********&v=beta&t=_cB4mvaKP0GlaHs5SrUTYgxS0uzbQe96YtS_UWW7Ozk", "expires_at": **********000}], "title": "SMU Chao Vietnam's 14th Executive Committee"}]}, {"school": "Academy of Holy Angels", "date": {}, "degree": "High School Diploma"}, {"school": "Le Quy Don High School for the Gifted-Danang", "date": {}, "degree": "High School Diploma"}, {"school": "University of St.Gallen", "date": {}, "degree": "International Student Exchange Program, Visiting Student"}], "honors": [], "volunteers": [{"organization": "Vietnamese Youth Alliance in Singapore (VNYA)", "title": "Marketing Coordinator", "duration": "3 mos", "description": "- Established marketing roadmaps and branding strategies for the event as well as collaborative plans with involved stakeholders\n- Created visual and written content, including overall themes for marketing collaterals, logo, and captions for social media posts, that helped to meet the registration goal of 50 participants", "date": {"start": "Jul 2021", "end": "Sep 2021"}}, {"organization": "Club Rainbow (Singapore)", "title": "Visual Communications Volunteer", "cause": "Social Services", "duration": "1 yr 3 mos", "description": "- Polishing and enhancing artworks created by the beneficiaries for fund-raising and promotional purposes\n- Designing collaterals for social media posts and website articles to engage audience and raise public awareness", "date": {"start": "Mar 2021", "end": "May 2022"}}, {"organization": "RiverLife Community Services", "title": "Tutor/Mentor Volunteer", "cause": "Education", "duration": "8 mos", "description": "- Facilitating one-to-one Mathematics lessons to a student\n- Providing emotional support and advice to the student and empowering her to follow her dreams and passions", "date": {"start": "Mar 2021", "end": "Oct 2021"}}, {"organization": "Let's Do It Da Nang", "title": "External Relations Coordinator", "cause": "Environment", "duration": "3 mos", "description": "- Sourced and contacted external organizations for sponsorship\n- Advised other teams on marketing and PR strategies as well as safety measures for the organization's event", "date": {"start": "Aug 2019", "end": "Oct 2019"}}, {"organization": "Wood Lake Nature Center", "title": "Naturalist Assistant Volunteer", "cause": "Environment", "duration": "1 yr 2 mos", "description": "- Managed visitor services such as welcoming and guiding visitors, taking donations, and recording registrations\n- Co-planned three conservation/natural education programs and camps for youth with about 50 participants each", "date": {"start": "May 2018", "end": "Jun 2019"}}, {"organization": "Hennepin County Library", "title": "Volunteer Administrative Assistant - Augsburg Park Library", "cause": "Education", "duration": "9 mos", "description": "- Managed patrons services such as welcoming and guiding visitors and checking in/out materials\n- Assessed the conditions, currency, and relevance of materials at the library", "date": {"start": "Jan 2017", "end": "Sep 2017"}}]}]