import psycopg2
import json
from datetime import datetime
from psycopg2.extras import RealDictCursor

# --- CONFIG ---
PROFILE_DB_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

# Keep only this version, at the top of your file
def get_start_date(exp):
    date = exp.get("date", {})
    start = date.get("start")
    

    if isinstance(start, str):
        for fmt in ("%b %Y", "%B %Y", "%Y"):  # Add "%Y" here
            try:
                parsed = datetime.strptime(start, fmt)
                return parsed
            except Exception as e:
                print(f"⚠️ Failed to parse '{start}' with format '{fmt}': {e}")
        return datetime.min


    elif isinstance(start, dict):
        try:
            year = start.get("year")
            month = start.get("month", 1)
            parsed = datetime(year, month, 1)
            print(f"✅ Parsed dict date {start} as {parsed}")
            return parsed
        except Exception as e:
            print(f"⚠️ Failed to parse dict start date {start}: {e}")
            return datetime.min

    print("⚠️ Unrecognized date format:", start)
    return datetime.min



# --- HELPER FUNCTIONS ---
def is_current_job(exp):
    end = exp.get("date", {}).get("end")
    if not end:
        return True
    if isinstance(end, str) and end.lower() in ["present", "current", "ongoing"]:
        return True
    return False


def get_most_recent_experience(experiences):
    valid_exps = [exp for exp in experiences if exp.get("date", {}).get("start")]    
    sorted_exp = sorted(valid_exps, key=get_start_date, reverse=True)
    return sorted_exp[0] if sorted_exp else {}


def estimate_age(birth_year, educations):
    current_year = datetime.now().year

    # 1. Use birth_year if present
    if birth_year:
        print(f"🎂 Birth year found: {birth_year}")
        return current_year - birth_year

    print("📚 No birth year, checking education dates...")
    start_years = []
    print(f"📦 Raw educations list (len={len(educations)}):")
    for idx, edu in enumerate(educations):
        print(f"  {idx+1}. {edu}")


    for edu in educations:

        start = edu.get("date", {}).get("start")
        print(f"🔍 Found education start: {start}")

        if isinstance(start, str):
            for fmt in ("%b %Y", "%B %Y", "%Y"):  # e.g. "Aug 2023"
                try:
                    parsed = datetime.strptime(start, fmt)
                    print(f"✅ Parsed '{start}' as year {parsed.year}")
                    start_years.append(parsed.year)
                    break
                except Exception as e:
                    print(f"⚠️ Failed parsing '{start}' with format '{fmt}': {e}")

        elif isinstance(start, dict):
            year = start.get("year")
            if isinstance(year, int):
                print(f"✅ Got year from dict: {year}")
                start_years.append(year)

    if start_years:
        earliest = min(start_years)
        age = current_year - earliest + 18
        print(f"🎓 Estimated age from earliest start year ({earliest}): {age}")
        return age

    print("❌ No valid education start year found.")
    return None




# --- MAIN LOGIC ---
conn = psycopg2.connect(PROFILE_DB_URL)
cursor = conn.cursor(cursor_factory=RealDictCursor)

cursor.execute("""
    SELECT id, full_name, location_city, location_country, birth_year, experiences, educations, created
    FROM linkedin_profiles    
""")

rows = cursor.fetchall()

results = []
updated_profiles = []

for row in rows:  # only update the first profile for testing
    profile = dict(row)
    # print("📝 Raw experiences (from DB):", profile.get("experiences"))

    # 2. Fill in location_country
    if not profile.get("location_country") and profile.get("location_city"):
        profile["location_country_filled"] = profile["location_city"]
    else:
        profile["location_country_filled"] = profile.get("location_country")

    # 3–5. Parse experiences
    raw_exp = profile.get("experiences")
    print("📥 Type of experiences:", type(raw_exp))

    if isinstance(raw_exp, str):
        try:
            experiences = json.loads(raw_exp)
            print(f"📋 Loaded {len(experiences)} experiences from JSON string.")
        except Exception as e:
            print(f"❌ Failed to parse JSON string: {e}")
            experiences = []
    elif isinstance(raw_exp, list):
        print(f"📋 Got {len(raw_exp)} experiences as native list.")
        experiences = raw_exp
    else:
        print("⚠️ Unrecognized type for experiences.")
        experiences = []

    latest_exp = get_most_recent_experience(experiences)
    profile["current_company"] = None
    profile["current_title"] = None
    profile["current_job_duration_years"] = None

    if latest_exp and is_current_job(latest_exp):        
        company_id = latest_exp.get("company", {}).get("id")
        company_name = latest_exp.get("company", {}).get("name")
        title = latest_exp.get("title")
        dt = get_start_date(latest_exp)

        profile["current_company"] = company_id
        profile["current_title"] = title
        profile["current_job_duration_years"] = round((datetime.now() - dt).days / 365, 2) if dt != datetime.min else None

        # --- CHECK IF THE COMPANY EXISTS IN THE COMPANIES TABLE ---
        print(f"🔍 Checking if company ID '{company_id}' exists in the companies table...")
        check_company_query = "SELECT company_id FROM linkedin_companies WHERE company_id = %s"
        cursor.execute(check_company_query, (company_id,))
        company_data = cursor.fetchone()

        

        # if not company_data:
        #     print(f"⚠️ Company ID '{company_id}' not found, inserting into linkedin_companies...")
        #     insert_company_query = """
        #         INSERT INTO linkedin_companies (company_id, name, updated_at)
        #         VALUES (%s, %s, %s) RETURNING company_id
        #     """
        #     cursor.execute(insert_company_query, (company_id, company_name, datetime.utcnow()))
        #     company_row_id = cursor.fetchone()[0]  # This will return the company_id
        #     print(f"✅ Inserted company '{company_name}' with company_id: {company_row_id}")
        # else:
        #     company_row_id = company_data["company_id"]
        #     print(f"✅ Company ID '{company_id}' already exists with company_id: {company_row_id}")

        if company_id:
            print(f"🔍 Checking if company ID '{company_id}' exists in the companies table...")
            check_company_query = "SELECT company_id FROM linkedin_companies WHERE company_id = %s"
            cursor.execute(check_company_query, (company_id,))
            company_data = cursor.fetchone()

            if not company_data:
                print(f"⚠️ Company ID '{company_id}' not found, inserting into linkedin_companies...")
                # insert_company_query = """
                #     INSERT INTO linkedin_companies (company_id, name, updated_at)
                #     VALUES (%s, %s, %s) RETURNING company_id
                # """
                # cursor.execute(insert_company_query, (company_id, company_name, datetime.utcnow()))
                # company_row = cursor.fetchone()
                # company_row_id = company_row["company_id"]
                # print(f"✅ Inserted company '{company_name}' with company_id: {company_row_id}")
            else:
                company_row_id = company_data["company_id"]
                print(f"✅ Company ID '{company_id}' already exists with company_id: {company_row_id}")
        else:
            print("❌ Skipping company insert: No company ID found.")



        profile["current_company_id"] = company_id  # This can be used later to update the profile


        # If company does not exist, insert it into the linkedin_companies table
        # if not company_data:
        #     print(f"⚠️ Company '{company}' not found in linkedin_companies, inserting...")
        #     insert_company_query = "INSERT INTO linkedin_companies (name) VALUES (%s) RETURNING id"
        #     cursor.execute(insert_company_query, (company,))
        #     company_id = cursor.fetchone()[0]
        #     print(f"✅ Inserted company with ID: {company_id}")
        # else:
        #     company_id = company_data["id"]
        #     print(f"✅ Company '{company}' already exists with ID: {company_id}")

        # Now update the profile with the company ID instead of the company name
        profile["current_company_id"] = company_id



    # 6. Age
    raw_edu = profile.get("educations")    
    try:
        educations = json.loads(raw_edu) if isinstance(raw_edu, str) else raw_edu        
    except Exception as e:        
        educations = []

    profile["age_estimated"] = estimate_age(profile.get("birth_year"), educations)

    # 7. updated_date
    profile["updated_date"] = datetime.utcnow().isoformat()

    # 8. created_date
    try:
        profile["created_date"] = datetime.fromtimestamp(profile["created"] / 1000).isoformat() if profile.get("created") else None
    except:
        profile["created_date"] = None
    print({
        "id": profile["id"],
        "current_company": profile["current_company"],
        "current_title": profile["current_title"],
        "current_job_duration_years": profile["current_job_duration_years"],
        "age": profile["age_estimated"],
    })
    # Perform DB update
    update_cursor = conn.cursor()
    update_cursor.execute("""
        UPDATE linkedin_profiles
        SET
            location_country = %s,
            current_company = %s,
            current_title = %s,
            current_job_duration_years = %s,
            age = %s,
            updated_date = %s,
            created_date = %s
        WHERE id = %s
    """, (
        profile["location_country_filled"],
        profile["current_company"],
        profile["current_title"],
        profile["current_job_duration_years"],
        profile["age_estimated"],
        profile["updated_date"],
        profile["created_date"],
        profile["id"]
    ))
    conn.commit()
    update_cursor.close()

    print(f"✅ Updated profile ID: {profile['id']} - {profile['full_name']}")
    updated_profiles.append({
        "id": profile["id"],
        "name": profile["full_name"],
        "location_country": profile["location_country_filled"],
        "current_company": profile["current_company"],
        "current_title": profile["current_title"],
        "current_job_duration_years": profile["current_job_duration_years"],
        "age": profile["age_estimated"],
        "updated_date": profile["updated_date"],
        "created_date": profile["created_date"]
    })


cursor.close()
conn.close()

with open("linkedin_profiles_updated_debug.json", "w", encoding="utf-8") as f:
    json.dump(updated_profiles, f, indent=2, ensure_ascii=False)

