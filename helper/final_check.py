import psycopg2
import json
from datetime import datetime
from psycopg2.extras import RealDictCursor
import requests
import openai
import os

# --- CONFIG ---
PROFILE_DB_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
RAPIDAPI_KEY = "**************************************************"
RAPIDAPI_HOST = "fresh-linkedin-scraper-api.p.rapidapi.com"
openai.api_key = os.getenv("OPENAI_API_KEY") or "********************************************************************************************************************************************************************" 

# --- LOGGING SETUP ---
debug_log = []
timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
debug_file = f"updated_profiles_{timestamp}.json"

# --- DB ---
conn = psycopg2.connect(PROFILE_DB_URL)
cursor = conn.cursor(cursor_factory=RealDictCursor)


def generate_summary(profile):
    try:
        prompt = f"""You are a helpful assistant that generates a summary of a LinkedIn profile.
The profile is:
{json.dumps(profile, indent=2)}
Output the summary in text format. Do not output any other text.
The summary should be a very detailed summary of the profile. The summary does not contain personal contact information.
"""
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            temperature=0.7,
                                messages=[
                                    {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": prompt}
            ]
        )
        summary_text = response.choices[0].message["content"]
        return summary_text
    except Exception as e:
        print(f"❌ Failed to generate summary: {e}")
        return None 

# --- HELPERS ---
def get_start_date(exp):
    date = exp.get("date", {})
    start = date.get("start")
    if isinstance(start, str):
        for fmt in ("%b %Y", "%B %Y", "%Y"):
            try:
                return datetime.strptime(start, fmt)
            except:
                continue
    elif isinstance(start, dict):
        try:
            return datetime(start.get("year"), start.get("month", 1), 1)
        except:
            return datetime.min
    return datetime.min

def is_current_job(exp):
    end = exp.get("date", {}).get("end")
    if not end:
        return True
    if isinstance(end, str) and end.strip().lower() in ["present", "current", "ongoing"]:
        return True
    return False

def fetch_or_insert_company(conn, company_id):
    cur = conn.cursor(cursor_factory=RealDictCursor)
    cur.execute("SELECT * FROM linkedin_companies WHERE company_id = %s", (company_id,))
    company = cur.fetchone()
    if company:
        return company

    # Fetch from API
    print(f"🌐 Fetching company {company_id} from API...")
    url = f"https://{RAPIDAPI_HOST}/api/v1/company/profile"
    headers = {
        "x-rapidapi-key": RAPIDAPI_KEY,
        "x-rapidapi-host": RAPIDAPI_HOST
    }
    try:
        res = requests.get(url, headers=headers, params={"company_id": str(company_id)}, timeout=10)
        if res.status_code == 200:
            data = res.json().get("data", {})
            cur.execute("""
                INSERT INTO linkedin_companies (company_id, universal_name, name, company_size, industry)
                VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (company_id) DO NOTHING
            """, (
                company_id,
                data.get("universal_name"),
                data.get("name"),
                data.get("employee_count"),
                (data.get("industries") or [None])[0]
            ))
            conn.commit()
            return data
    except Exception as e:
        print(f"❌ API error: {e}")
    return None

def extract_company_list(experiences):
    company_ids = []
    for exp in experiences:
        company = exp.get("company")
        if isinstance(company, dict):
            cid = company.get("id")
            print(f"🏢 Extracted company ID: {cid}")
            if cid:
                company_ids.append(cid)
    return list(set(company_ids))  # De-duplicate


def extract_school_list(educations):
    school_names = []
    for edu in educations:
        school = edu.get("school")
        print(f"🎓 Extracted school: {school}")
        if isinstance(school, str) and school.strip():
            school_names.append(school.strip())
    return list(set(school_names))


def get_most_recent_experience(experiences):
    valid_exps = [exp for exp in experiences if exp.get("date", {}).get("start")]    
    sorted_exp = sorted(valid_exps, key=get_start_date, reverse=True)
    return sorted_exp[0] if sorted_exp else {}


def is_student(educations):
    now = datetime.now()

    for edu in educations:
        end = edu.get("date", {}).get("end")

        if not end:
            return True  # no end date → probably still studying

        if isinstance(end, str):
            if end.lower() in ["present", "ongoing", "current"]:
                return True
            for fmt in ("%Y", "%b %Y", "%B %Y"):
                try:
                    parsed = datetime.strptime(end, fmt)
                    if parsed > now:
                        return True  # ends in the future → still a student
                    break
                except:
                    continue

        elif isinstance(end, dict):
            year = end.get("year")
            month = end.get("month", 1)
            try:
                end_date = datetime(year, month, 1)
                if end_date > now:
                    print(f"is student = TRUE")
                    return True
            except:
                continue
    print(f"is student = FALSE")
    return False

def get_graduation_year(educations):
    years = []

    for edu in educations:
        end = edu.get("date", {}).get("end")

        if isinstance(end, str):
            try:
                # Try to parse "2015" or "May 2015"
                for fmt in ("%Y", "%b %Y", "%B %Y"):
                    try:
                        parsed = datetime.strptime(end, fmt)
                        years.append(parsed.year)
                        break
                    except:
                        continue
            except:
                continue

        elif isinstance(end, dict):
            year = end.get("year")
            if isinstance(year, int):
                years.append(year)    
    return max(years) if years else None

def enrich_profile(profile):
    profile_id = profile["id"]
    print(f"\n🔍 Processing: {profile['full_name']}")

    raw_exps = profile.get("experiences")
    raw_edu = profile.get("educations")

    # Add debug
    print(f"📄 raw_exps type = {type(raw_exps)}, value = {raw_exps}")
    print(f"📄 raw_edu type = {type(raw_edu)}, value = {raw_edu}")

    try:
        experiences = raw_exps if isinstance(raw_exps, list) else json.loads(raw_exps or "[]")
    except Exception as e:
        print(f"❌ Failed to parse experiences JSON: {e}")
        experiences = []

    try:
        educations = raw_edu if isinstance(raw_edu, list) else json.loads(raw_edu or "[]")
    except Exception as e:
        print(f"❌ Failed to parse educations JSON: {e}")
        educations = []


    latest_exp = next((exp for exp in experiences if is_current_job(exp)), None)
    if latest_exp:
        print(f"📅 is_current_job = {is_current_job(latest_exp)}")
    else:
        print("⚠️ No current job found in experiences.")    

    current_company_id = None
    current_title = None
    job_duration = None
    company_data = None

    if latest_exp and is_current_job(latest_exp):
        company_id = latest_exp.get("company", {}).get("id")
        print(f"🏢 company_id: {company_id}")
        if company_id:
            company_data = fetch_or_insert_company(conn, company_id)
            if company_data:
                current_company_id = company_id
                current_title = latest_exp.get("title")
                dt = get_start_date(latest_exp)
                job_duration = round((datetime.now() - dt).days / 365, 2) if dt != datetime.min else None
            else:
                print(f"⚠️ Skipping profile {profile_id} due to missing company info")
                return

    # Compute extra fields
    def get_years_of_exp(exps):
        dates = [get_start_date(e) for e in exps if get_start_date(e) != datetime.min]
        return round((datetime.now() - min(dates)).days / 365, 2) if dates else 0.0

    def get_highest_degree(eds):
        levels = ["doctor", "phd", "master", "bachelor", "associate"]
        for edu in sorted(eds, key=lambda e: get_start_date(e), reverse=True):
            deg = (edu.get("degree") or "").lower()
            for level in levels:
                if level in deg:
                    return level
        return None
    
    def get_field_of_study(educations):
        for edu in reversed(educations):
            degree = edu.get("degree")
            if isinstance(degree, str) and degree.strip():      
                print(f"🔍 Found field of study: {degree.strip()}")          
                return degree.strip()
        return None

    def estimate_age(birth_year, educations):
        current_year = datetime.now().year

        # 1. Use birth_year if present
        if birth_year:
            print(f"🎂 Birth year found: {birth_year}")
            return current_year - birth_year

        print("📚 No birth year, checking education dates...")
        start_years = []
        print(f"📦 Raw educations list (len={len(educations)}):")
        for idx, edu in enumerate(educations):
            print(f"  {idx+1}. {edu}")


        for edu in educations:

            start = edu.get("date", {}).get("start")
            print(f"🔍 Found education start: {start}")

            if isinstance(start, str):
                for fmt in ("%b %Y", "%B %Y", "%Y"):  # e.g. "Aug 2023"
                    try:
                        parsed = datetime.strptime(start, fmt)
                        print(f"✅ Parsed '{start}' as year {parsed.year}")
                        start_years.append(parsed.year)
                        break
                    except Exception as e:
                        print(f"⚠️ Failed parsing '{start}' with format '{fmt}': {e}")

            elif isinstance(start, dict):
                year = start.get("year")
                if isinstance(year, int):
                    print(f"✅ Got year from dict: {year}")
                    start_years.append(year)

        if start_years:
            earliest = min(start_years)
            age = current_year - earliest + 18
            print(f"🎓 Estimated age from earliest start year ({earliest}): {age}")
            return age

        print("❌ No valid education start year found.")
        return None


    
    # Created date
    created_date = None
    if profile.get("created"):
        try:
            created_date = datetime.fromtimestamp(profile["created"] / 1000).isoformat()
        except:
            pass

    # Fallback for location_country
    location_country_filled = profile.get("location_country") or profile.get("location_city")


    company_list = extract_company_list(experiences)
    school_list = extract_school_list(educations)
    summary_text = generate_summary(profile)

    current_timestamp = datetime.utcnow().isoformat()

    update_cursor = conn.cursor()
    update_cursor.execute("""
    UPDATE linkedin_profiles
        SET
            current_company = %s,
            current_title = %s,
            current_job_duration_years = %s,
            yoe = %s,
            highest_degree = %s,
            age = %s,
            is_student= %s,
            graduation_year= %s,
            updated_date = %s,
            created_date = %s,
            field_of_study= %s,
            location_country = %s,
            company_list = %s,
            school_list = %s,
            summary = %s,
            enriched = true            
        WHERE id = %s
    """, (
        current_company_id,
        current_title,
        job_duration,
        get_years_of_exp(experiences),
        get_highest_degree(educations),
        estimate_age(profile.get("birth_year"),educations),
        is_student(educations),
        get_graduation_year(educations),
        current_timestamp,
        created_date,
        get_field_of_study(educations),
        location_country_filled,
        json.dumps(company_list),
        json.dumps(school_list),      
        summary_text,
        profile_id
    ))

    conn.commit()
    update_cursor.close()
    print(f"✅ Updated: {profile['full_name']} (ID: {profile_id})")

    # --- Logging
    debug_log.append({
        "id": profile_id,
        "full_name": profile["full_name"],
        "current_company": current_company_id,
        "current_title": current_title,
        "current_job_duration_years": job_duration,
        "yoe": get_years_of_exp(experiences),
        "highest_degree": get_highest_degree(educations),
        "age": estimate_age(profile.get("birth_year"),educations),
        "graduation_year": get_graduation_year(educations),
        "is_student": is_student(educations),
        "created_date": created_date,
        "field_of_study": get_field_of_study(educations),
        "location_country": location_country_filled,
        "company_list": company_list,
        "school_list": school_list,
        "updated_date": current_timestamp,        
        "summary": summary_text,
    })

# --- Run All Profiles ---
BATCH_SIZE = 20
while True:
    cursor.execute("""
        SELECT * FROM linkedin_profiles
        WHERE enriched = false
        ORDER BY id
        LIMIT %s
    """, (BATCH_SIZE,))
    rows = cursor.fetchall()

    if not rows:
        break  # All done

    for row in rows:
        enrich_profile(row)

cursor.close()
conn.close()

# --- Dump Debug Log
with open(debug_file, "w", encoding="utf-8") as f:
    json.dump(debug_log, f, indent=2, ensure_ascii=False)
print(f"📁 Saved debug log → {debug_file}")
