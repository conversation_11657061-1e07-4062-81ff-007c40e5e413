import psycopg2
import json
from datetime import datetime
from psycopg2.extras import RealDictCursor
import openai
import os
from multiprocessing import Pool, cpu_count
# from tqdm import tqdm  # Uncomment this if you want a progress bar

# --- CONFIG ---
DB_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
openai.api_key = os.getenv("OPENAI_API_KEY") or "********************************************************************************************************************************************************************"

def clean_profile_for_prompt(profile):
    cleaned = {
        "id": profile.get("id"),
        "full_name": profile.get("full_name"),
        "birth_year": profile.get("birth_year"),
        "experiences": profile.get("experiences", [])[:5],  # only first 5 experiences
        "educations": profile.get("educations", [])[:3],    # only first 3 educations
    }
    return cleaned


# --- GENERATE SUMMARY FUNCTION ---
def generate_summary_for_profile(profile):
    profile_id = profile["id"]
    full_name = profile["full_name"]

    for attempt in range(2):  # retry once if error
        try:
            prompt = f"""You are a helpful assistant that generates a summary of a LinkedIn profile.
The profile is:
{json.dumps(clean_profile_for_prompt(profile), indent=2)}
Output the summary in markdown format. Do not output any other text.
The summary should be a very detailed summary of the profile. The summary does not contain personal contact information."""

            response = openai.chat.completions.create(
                model="gpt-3.5-turbo",
                temperature=0.7,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": prompt}
                ]
            )

            summary_text = response.choices[0].message.content.strip()

            return {
                "id": profile_id,
                "summary": summary_text,
                "status": "success",
                "full_name": full_name
            }

        except Exception as e:
            print(f"⚠️ Retry {attempt+1} failed for {full_name}: {e}")
            if attempt == 1:
                return {
                    "id": profile_id,
                    "summary": None,
                    "status": f"error: {e}",
                    "full_name": full_name
                }

# --- DB FETCH FUNCTION ---
def fetch_profiles_without_summary():
    conn = psycopg2.connect(DB_URL)
    cur = conn.cursor(cursor_factory=RealDictCursor)
    cur.execute("""
        SELECT id, full_name, birth_year, experiences, educations
        FROM linkedin_profiles
        WHERE summary IS NULL OR summary = ''
    """)
    rows = cur.fetchall()
    cur.close()
    conn.close()
    return rows

# --- DB UPDATE FUNCTION ---
def update_summary_in_db(profile_id, summary):
    try:
        conn = psycopg2.connect(DB_URL)
        cur = conn.cursor()
        cur.execute("UPDATE linkedin_profiles SET summary = %s, updated_date = %s WHERE id = %s", (
            summary,
            datetime.utcnow().isoformat(),
            profile_id
        ))
        conn.commit()
        cur.close()
        conn.close()
        print(f"✅ Updated summary for ID: {profile_id}")
    except Exception as e:
        print(f"❌ Failed DB update for ID {profile_id}: {e}")

# --- MAIN ---
if __name__ == "__main__":
    profiles = fetch_profiles_without_summary()
    print(f"📦 Found {len(profiles)} profiles without summary.")

    if not profiles:
        exit()

    worker_count = min(cpu_count(), 8)
    print(f"⚙️ Using {worker_count} parallel workers...")

    with Pool(worker_count) as pool:
        # for result in tqdm(pool.imap_unordered(generate_summary_for_profile, profiles), total=len(profiles)):  # progress bar
        for result in pool.imap_unordered(generate_summary_for_profile, profiles):
            if result["status"] == "success" and result["summary"]:
                update_summary_in_db(result["id"], result["summary"])
            else:
                print(f"⚠️ Failed to generate summary for {result['full_name']}: {result['status']}")
