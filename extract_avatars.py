import psycopg2
import json

# ---- CONFIG ----
DB_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
OUTPUT_FILE = "best_avatars.json"

# ---- HELPERS ----
def get_largest_avatar_url(avatars):
    try:
        if isinstance(avatars, str):
            avatars = json.loads(avatars)
        sorted_avatars = sorted(avatars, key=lambda a: a.get("width", 0), reverse=True)
        return sorted_avatars[0].get("url") if sorted_avatars else None
    except Exception as e:
        print(f"⚠️ Failed parsing avatar list: {e}")
        return None

# ---- MAIN ----
def extract_best_avatars():
    conn = psycopg2.connect(DB_URL)
    cursor = conn.cursor()

    cursor.execute("""
        SELECT id, public_identifier, linkedin_url, avatar
        FROM linkedin_profiles
        WHERE avatar IS NOT NULL
    """)

    rows = cursor.fetchall()
    results = []

    for profile_id, public_id, linkedin_url, avatar_json in rows:
        best_avatar_url = get_largest_avatar_url(avatar_json)
        if best_avatar_url:
            results.append({
                "id": profile_id,
                "public_identifier": public_id,
                "linkedin_url": linkedin_url,
                "avatar_url": best_avatar_url
            })

    with open(OUTPUT_FILE, "w") as f:
        json.dump(results, f, indent=2)

    print(f"✅ Extracted {len(results)} avatar URLs to {OUTPUT_FILE}")
    cursor.close()
    conn.close()

if __name__ == "__main__":
    extract_best_avatars()
