import psycopg2

# DB connection strings
PROFILE_DB_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
DATABASE_URL = "postgresql://beyz-web_owner:<EMAIL>/beyz-production?sslmode=require"

LOG_FILE = "debug_output.txt"

def log(msg: str):
    print(msg)
    with open(LOG_FILE, "a", encoding="utf-8") as f:
        f.write(msg + "\n")

def ensure_columns_exist():
    conn = psycopg2.connect(PROFILE_DB_URL)
    cursor = conn.cursor()
    cursor.execute("""
        ALTER TABLE linkedin_profiles
        ADD COLUMN IF NOT EXISTS email TEXT,
        ADD COLUMN IF NOT EXISTS linkedin_url TEXT;
    """)
    conn.commit()
    cursor.close()
    conn.close()
    log("✅ Ensured email and linkedin_url columns exist.")

def load_headline_profiles(cursor):
    cursor.execute("SELECT full_name, headline, public_identifier FROM linkedin_profiles WHERE headline IS NOT NULL")
    profiles = cursor.fetchall()
    log(f"📥 Loaded {len(profiles)} linkedin_profiles into memory.")
    return profiles

def update_by_headline_in_title():
    source_conn = psycopg2.connect(DATABASE_URL)
    source_cursor = source_conn.cursor()

    target_conn = psycopg2.connect(PROFILE_DB_URL)
    target_cursor = target_conn.cursor()

    # Cache linkedin_profiles once
    headline_profiles = load_headline_profiles(target_cursor)

    BATCH_SIZE = 10000
    offset = 0
    updated = 0
    skipped = 0

    while True:
        source_cursor.execute(f'''
            SELECT title, email, "linkedinURL"
            FROM "mailingList"
            WHERE title IS NOT NULL AND "linkedinURL" IS NOT NULL
            ORDER BY email
            LIMIT {BATCH_SIZE} OFFSET {offset}
        ''')
        rows = source_cursor.fetchall()
        if not rows:
            break

        log(f"\n📦 Processing batch OFFSET {offset}, size: {len(rows)}")

        for i, (title, email, linkedin_url) in enumerate(rows, start=offset+1):
            title_clean = title.strip().lower()
            log(f"\n[{i}] 📌 Checking title = '{title_clean}'")

            matches = [
                (fn, hl, pid)
                for (fn, hl, pid) in headline_profiles
                if hl and hl.strip().lower() == title_clean
            ]

            if len(matches) == 1:
                full_name, headline, public_identifier = matches[0]

                log(f"🔧 Updating public_identifier: {public_identifier}")
                log(f"   - email: {email}")
                log(f"   - linkedin_url: {linkedin_url}")

                target_cursor.execute("""
                    UPDATE linkedin_profiles
                    SET email = %s, linkedin_url = %s
                    WHERE public_identifier = %s
                """, (email, linkedin_url, public_identifier))

                target_cursor.execute("""
                    SELECT email, linkedin_url
                    FROM linkedin_profiles
                    WHERE public_identifier = %s
                """, (public_identifier,))
                log(f"🧾 DB says → {target_cursor.fetchone()}")

                updated += 1
                log(f"✅ Updated profile (public_identifier: {public_identifier})")

            elif len(matches) > 1:
                skipped += 1
                log(f"⚠️ Skipped: multiple profiles where headline found in title")
                for idx, (full_name, headline, public_identifier) in enumerate(matches, start=1):
                    log(f"   [{idx}] full_name: {full_name}, headline: {headline}, public_identifier: {public_identifier}")
            else:
                skipped += 1
                log(f"⚠️ Skipped: no profile found with headline in title")

        offset += BATCH_SIZE

    target_conn.commit()
    log("\n🎯 Summary:")
    log(f"✅ Updated {updated} profile(s)")
    log(f"⚠️ Skipped {skipped} profile(s)")

    source_cursor.close()
    source_conn.close()
    target_cursor.close()
    target_conn.close()


if __name__ == "__main__":
    # Clear log file before running
    with open(LOG_FILE, "w", encoding="utf-8") as f:
        f.write("🔧 Debug Log Start\n\n")

    ensure_columns_exist()
    update_by_headline_in_title()
