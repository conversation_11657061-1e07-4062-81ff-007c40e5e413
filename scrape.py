import requests
import psycopg2
import json
from multiprocessing import Process, Manager
import math
import backoff
from psycopg2 import pool

# Global connection pools
profile_db_pool = None
main_db_pool = None

# Initialize DB connection pools (should be called early)
def init_connection_pools():
    global profile_db_pool, main_db_pool
    profile_db_pool = psycopg2.pool.SimpleConnectionPool(1, 10, PROFILE_DB_URL)
    main_db_pool = psycopg2.pool.SimpleConnectionPool(1, 10, DATABASE_URL)

# ---------- CONSTANTS ----------
PROFILE_DB_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
DATABASE_URL = "postgresql://beyz-web_owner:<EMAIL>/beyz-production?sslmode=require"

EMAIL_VERIFIER_API = "https://api.millionverifier.com/api/v3/?api=uRRSTeKLdlQAiQGBCfp0Cu5u1&email={email}&timeout=10"

LINKEDIN_API_URL = "https://fresh-linkedin-scraper-api.p.rapidapi.com/api/v1/user/profile"
HEADERS = {
    "x-rapidapi-key": "**************************************************",
    "x-rapidapi-host": "fresh-linkedin-scraper-api.p.rapidapi.com"
}

# ---------- FUNCTIONS ----------
def validate_email(email):
    try:
        response = requests.get(EMAIL_VERIFIER_API.format(email=email), timeout=10)
        if response.status_code == 200:
            data = response.json()
            return data.get("result") == "ok"
        print(f"Email validation failed: {response.text}")
        return False
    except Exception as e:
        print(f"Error validating email {email}: {e}")
        return False

@backoff.on_exception(backoff.expo, requests.exceptions.RequestException, max_tries=3)
def scrape_linkedin_profile(username):
    params = {
        "username": username,
        "include_experiences": "true",
        "include_skills": "true",
        "include_certifications": "true",
        "include_educations": "true",
        "include_publications": "true",
        "include_honors": "true",
        "include_volunteers": "true"
    }
    response = requests.get(LINKEDIN_API_URL, headers=HEADERS, params=params, timeout=10)
    if response.status_code == 200:
        return response.json()
    print(f"Failed to scrape LinkedIn profile: {response.status_code} - {response.text}")
    return None

def ensure_table_exists():
    conn = psycopg2.connect(PROFILE_DB_URL)
    cursor = conn.cursor()
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS linkedin_profiles (
      id TEXT PRIMARY KEY,
      email TEXT,
      linkedin_url TEXT,
      urn TEXT,
      public_identifier TEXT,
      first_name TEXT,
      last_name TEXT,
      full_name TEXT,
      headline TEXT,
      is_premium BOOLEAN,
      is_open_to_work BOOLEAN,
      is_hiring BOOLEAN,
      is_memorialized BOOLEAN,
      is_influencer BOOLEAN,
      is_top_voice BOOLEAN,
      is_creator BOOLEAN,
      birth_day INT,
      birth_month INT,
      birth_year INT,
      pronoun TEXT,
      created BIGINT,
      created_date TIMESTAMP,
      location_country TEXT,
      location_country_code TEXT,
      location_city TEXT,
      avatar JSONB,
      cover JSONB,
      associated_hashtag TEXT[],
      website_title TEXT,
      website_url TEXT,
      supported_locales JSONB,
      primary_locale_country TEXT,
      primary_locale_language TEXT,
      follower_count BIGINT,
      connection_count BIGINT,
      experiences JSONB,
      skills JSONB,
      certifications JSONB,
      publications JSONB,
      educations JSONB,
      honors JSONB,
      volunteers JSONB
    );
    """)
    conn.commit()
    cursor.close()
    conn.close()
    print("✅ Ensured linkedin_profiles table exists.")

def save_profile_to_db(profile_data, email=None, linkedin_url=None):
    ensure_table_exists()  # ensure table before inserting

    conn = psycopg2.connect(PROFILE_DB_URL)
    cursor = conn.cursor()
    cursor.execute("""
    INSERT INTO linkedin_profiles (
      id, email, linkedin_url, urn, public_identifier, first_name, last_name, full_name, headline,
      is_premium, is_open_to_work, is_hiring, is_memorialized, is_influencer,
      is_top_voice, is_creator, birth_day, birth_month, birth_year, pronoun,
      created, created_date, location_country, location_country_code, location_city,
      avatar, cover, associated_hashtag, website_title, website_url,
      supported_locales, primary_locale_country, primary_locale_language,
      follower_count, connection_count, experiences, skills, certifications,
      publications, educations, honors, volunteers
    ) VALUES (
      %(id)s, %(email)s, %(linkedin_url)s, %(urn)s, %(public_identifier)s, %(first_name)s, %(last_name)s, %(full_name)s, %(headline)s,
      %(is_premium)s, %(is_open_to_work)s, %(is_hiring)s, %(is_memorialized)s, %(is_influencer)s,
      %(is_top_voice)s, %(is_creator)s, %(birth_day)s, %(birth_month)s, %(birth_year)s, %(pronoun)s,
      %(created)s, to_timestamp(%(created)s / 1000), %(location_country)s, %(location_country_code)s, %(location_city)s,
      %(avatar)s, %(cover)s, %(associated_hashtag)s, %(website_title)s, %(website_url)s,
      %(supported_locales)s, %(primary_locale_country)s, %(primary_locale_language)s,
      %(follower_count)s, %(connection_count)s, %(experiences)s, %(skills)s, %(certifications)s,
      %(publications)s, %(educations)s, %(honors)s, %(volunteers)s
    )
    ON CONFLICT (id) DO NOTHING
    """, {
        "id": profile_data.get("id"),
        "email": email,
        "linkedin_url": linkedin_url,
        "urn": profile_data.get("urn"),
        "public_identifier": profile_data.get("public_identifier"),
        "first_name": profile_data.get("first_name"),
        "last_name": profile_data.get("last_name"),
        "full_name": profile_data.get("full_name"),
        "headline": profile_data.get("headline"),
        "is_premium": profile_data.get("is_premium"),
        "is_open_to_work": profile_data.get("is_open_to_work"),
        "is_hiring": profile_data.get("is_hiring"),
        "is_memorialized": profile_data.get("is_memorialized"),
        "is_influencer": profile_data.get("is_influencer"),
        "is_top_voice": profile_data.get("is_top_voice"),
        "is_creator": profile_data.get("is_creator"),
        "birth_day": profile_data.get("birth", {}).get("day"),
        "birth_month": profile_data.get("birth", {}).get("month"),
        "birth_year": profile_data.get("birth", {}).get("year"),
        "pronoun": profile_data.get("pronoun"),
        "created": profile_data.get("created"),
        "location_country": profile_data.get("location", {}).get("country"),
        "location_country_code": profile_data.get("location", {}).get("country_code"),
        "location_city": profile_data.get("location", {}).get("city"),
        "avatar": json.dumps(profile_data.get("avatar", [])),
        "cover": json.dumps(profile_data.get("cover", [])),
        "associated_hashtag": profile_data.get("associated_hashtag", []),
        "website_title": profile_data.get("website", {}).get("title"),
        "website_url": profile_data.get("website", {}).get("url"),
        "supported_locales": json.dumps(profile_data.get("supported_locales", [])),
        "primary_locale_country": profile_data.get("primary_locale", {}).get("country"),
        "primary_locale_language": profile_data.get("primary_locale", {}).get("language"),
        "follower_count": profile_data.get("follower_and_connection", {}).get("follower_count"),
        "connection_count": profile_data.get("follower_and_connection", {}).get("connection_count"),
        "experiences": json.dumps(profile_data.get("experiences", []), ensure_ascii=False),
        "skills": json.dumps(profile_data.get("skills", []), ensure_ascii=False),
        "certifications": json.dumps(profile_data.get("certifications", [])),
        "publications": json.dumps(profile_data.get("publications", [])),
        "educations": json.dumps(profile_data.get("educations", [])),
        "honors": json.dumps(profile_data.get("honors", [])),
        "volunteers": json.dumps(profile_data.get("volunteers", []))
    })
    conn.commit()
    cursor.close()
    conn.close()
    print("✅ profile data inserted!")

def worker(offset_start: int, offset_end: int):
    # Create fresh connections for this worker process
    conn = psycopg2.connect(DATABASE_URL)
    cursor = conn.cursor()

    profile_conn = psycopg2.connect(PROFILE_DB_URL)
    profile_cursor = profile_conn.cursor()
    profile_cursor.execute('SELECT public_identifier FROM linkedin_profiles')
    existing_profiles = set(row[0] for row in profile_cursor.fetchall())
    profile_cursor.close()
    profile_conn.close()

    batch_size = 100
    offset = offset_start
    scraped_profiles = []
    scraped_count = 0

    while offset < offset_end:
        cursor.execute(f'SELECT email, "linkedinURL" FROM "mailingList" LIMIT {batch_size} OFFSET {offset}')
        rows = cursor.fetchall()
        if not rows:
            break

        for email, linkedin_url in rows:
            if not linkedin_url:
                continue
            username = linkedin_url.rstrip("/").split("/")[-1]
            print(f"👤 [Worker {offset_start}-{offset_end}] Processing: {username}")
            if username in existing_profiles:
                continue
            if not validate_email(email):
                continue

            scraped_data = scrape_linkedin_profile(username)
            if scraped_data and scraped_data.get("data"):
                profile_data = scraped_data["data"]
                save_profile_to_db(profile_data, email=email, linkedin_url=linkedin_url)
                existing_profiles.add(username)
                scraped_profiles.append(profile_data)
                scraped_count += 1

        offset += batch_size

    cursor.close()
    conn.close()

    with open(f"scraped_debug_{offset_start}_{offset_end}.json", "w") as f:
        json.dump(scraped_profiles, f, indent=2)

    print(f"✅ Worker {offset_start}-{offset_end} finished.")
    return scraped_count

def wrapped_worker(start, end, scraped_counts):
    count = worker(start, end)
    scraped_counts.append(count)

def main():
    # Note: Connection pools are not used in multiprocessing due to sharing issues
    # Each worker will create its own connections

    rows_to_scrape = 5
    num_workers = 5
    batch_size = rows_to_scrape // num_workers

    manager = Manager()
    scraped_counts = manager.list()

    processes = []
    for i in range(num_workers):
        start = i * batch_size
        end = (i + 1) * batch_size if i < num_workers - 1 else rows_to_scrape
        p = Process(target=wrapped_worker, args=(start, end, scraped_counts))
        processes.append(p)
        p.start()

    for p in processes:
        p.join()

    total_scraped = sum(scraped_counts)
    print(f"🎉 All workers finished. Total profiles scraped: {total_scraped}")



# def main():
#     conn = psycopg2.connect(DATABASE_URL)
#     cursor = conn.cursor()
#     print("🔌 Connected to source database!")

#     # get already-scraped public_identifiers
#     profile_conn = psycopg2.connect(PROFILE_DB_URL)
#     profile_cursor = profile_conn.cursor()
#     profile_cursor.execute('SELECT public_identifier FROM linkedin_profiles')
#     existing_profiles = set(row[0] for row in profile_cursor.fetchall())
#     profile_cursor.close()
#     profile_conn.close()
#     print(f"⚠️ Found {len(existing_profiles)} profiles already scraped. Will skip them.")

#     batch_size = 1000
#     offset = 0
#     total_scraped = 0
#     scraped_profiles = []  # for debugging JSON file

#     while total_scraped < 3000:
#         cursor.execute(f'SELECT email, "linkedinURL" FROM "mailingList" LIMIT {batch_size} OFFSET {offset}')
#         rows = cursor.fetchall()
#         if not rows:
#             break  # no more data

#         for email, linkedin_url in rows:
#             if not linkedin_url:
#                 continue
#             username = linkedin_url.rstrip("/").split("/")[-1]
#             if username in existing_profiles:
#                 continue
#             if not validate_email(email):
#                 continue

#             scraped_data = scrape_linkedin_profile(username)
#             if scraped_data and scraped_data.get("data"):
#                 profile_data = scraped_data["data"]
#                 scraped_profiles.append(profile_data)
#                 save_profile_to_db(profile_data, email=email, linkedin_url=linkedin_url)

#                 existing_profiles.add(username)  # avoid duplicates in next rounds
#                 total_scraped += 1
#                 print(f"✅ Scraped profile {total_scraped}: {username}")

#                 if total_scraped >= 1000:
#                     break
#         offset += batch_size

#     # save all scraped profiles to JSON file
#     with open("scraped_profiles_debug.json", "w") as f:
#         json.dump(scraped_profiles, f, indent=2)
#     print("✅ All scraped profiles saved to scraped_profiles_debug.json.")

#     cursor.close()
#     conn.close()
#     print("✅ Done!")

if __name__ == "__main__":
    main()
