import json
import tempfile
import requests
from deepface import DeepFace
import psycopg2
from PIL import Image
from io import BytesIO
import datetime
import os

# ---- CONFIG ----
DB_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
INPUT_FILE = "best_avatars.json"
LOG_FILE = "gender_debug_log.txt"

# ---- LOGGING ----
def log(message):
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    with open(LOG_FILE, "a", encoding="utf-8") as logf:
        logf.write(f"[{timestamp}] {message}\n")
    print(message)

# ---- FUNCTIONS ----
def get_gender_from_image_url(image_url):
    try:
        response = requests.get(image_url.strip(), stream=True, timeout=10)

        content_type = response.headers.get("Content-Type", "")
        if response.status_code != 200 or "image" not in content_type:
            log(f"⚠️ Not a valid image ({content_type}): {image_url}")
            return None

        content_length = int(response.headers.get("Content-Length", 0))
        if content_length < 1000:
            log(f"⚠️ Image too small or empty: {image_url}")
            return None

        try:
            Image.open(BytesIO(response.content)).verify()
        except Exception:
            log(f"❌ PIL cannot open image (corrupted?): {image_url}")
            return None

        with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as tmp:
            tmp.write(response.content)
            tmp.flush()
            tmp_path = tmp.name

        try:
            result = DeepFace.analyze(
                img_path=tmp_path,
                actions=["gender"],
                enforce_detection=False,
                detector_backend="opencv"
            )
            os.remove(tmp_path)

            gender_data = result[0].get("gender")
            if isinstance(gender_data, dict):
                gender = max(gender_data, key=gender_data.get)
            else:
                gender = gender_data
            return gender

        except Exception as e:
            log(f"❌ DeepFace error for {image_url}: {e}")
            if os.path.exists(tmp_path):
                os.remove(tmp_path)
            return None

    except Exception as e:
        log(f"❌ Error downloading image {image_url}: {e}")
        return None



def update_gender_in_db(profile_id, gender):
    try:
        conn = psycopg2.connect(DB_URL)
        cursor = conn.cursor()
        cursor.execute("""
            UPDATE linkedin_profiles
            SET gender = %s
            WHERE id = %s
        """, (gender, profile_id))
        conn.commit()
        cursor.close()
        conn.close()
        log(f"✅ Updated gender → {gender} for ID: {profile_id}")
    except Exception as e:
        log(f"❌ DB update failed for ID {profile_id}: {e}")


def process_profiles():
    with open(INPUT_FILE, "r") as f:
        data = json.load(f)

    log(f"📦 Loaded {len(data)} avatar entries from JSON.")

    for profile in data:
        profile_id = profile.get("id")
        avatar_url = profile.get("avatar_url")

        if not profile_id or not avatar_url:
            log(f"⛔ Skipping invalid entry: {profile}")
            continue

        gender = get_gender_from_image_url(avatar_url)
        if gender:
            update_gender_in_db(profile_id, gender)
        else:
            log(f"⚠️ Skipped DB update due to no gender for ID: {profile_id}")

# ---- MAIN ----
if __name__ == "__main__":
    if os.path.exists(LOG_FILE):
        os.remove(LOG_FILE)  # Reset log each time
    process_profiles()
